# FP16 Mixed Precision Training Guide

This guide explains how to use FP16 mixed precision training in the DataYatesV1 codebase for improved memory efficiency and training speed.

## Overview

FP16 (half-precision) training reduces memory usage by ~50% and can provide significant speedups on modern GPUs while maintaining training stability through careful implementation of guardrails.

## Quick Start

To enable FP16 training for multidataset models:

```bash
python examples/model_train_multi.py --train-config path/to/multidataset_train_config.yaml --fp16
```

**Note**: The script now uses the new unified configuration structure where `--train-config` points to a multidataset training configuration file that contains both model config and dataset config paths.

## Implementation Details

### Core Components

1. **Lightning Trainer Configuration**
   - Uses `precision="16-mixed"` for automatic mixed precision
   - Enables gradient clipping with `gradient_clip_val=1.0`

2. **Dataset Casting**
   - Stimulus data (`stim`) is cast to FP16 for memory efficiency
   - Target data (`robs`) and masks (`dfs`) remain in FP32 for numerical stability

3. **Critical Component Protection**
   - BatchNorm layers forced to FP32
   - Custom normalization layers (RMSNorm, LayerNorm, GlobalResponseNorm) compute in FP32
   - Loss computation always performed in FP32
   - Readout layers operate in FP32

### Guardrails Implemented

#### ✅ Automatic Safeguards
- **FP16SafetyCallback**: Automatically forces critical components to FP32
- **GradientMonitorCallback**: Monitors for NaN/Inf gradients and tracks gradient norms
- **Autocast Protection**: Loss computation wrapped in FP32 autocast contexts
- **Learning Rate Adjustment**: Automatically reduces LR from 5e-4 to 2.5e-4 for stability

#### ✅ Custom Normalization Layers
All custom normalization layers have been modified to:
- Perform computations in FP32 internally
- Convert back to input dtype for memory efficiency
- Maintain numerical stability

#### ✅ Loss Function Protection
Both single and multidataset Lightning modules:
- Cast predictions and targets to FP32 before loss computation
- Use autocast contexts to disable FP16 during loss calculation
- Preserve gradient flow while ensuring numerical stability

### Memory Optimization

The implementation provides significant memory savings:
- **Stimulus data**: ~50% memory reduction (FP16)
- **Model weights**: Mixed precision (FP16 forward, FP32 gradients)
- **Activations**: Automatic mixed precision handling
- **Critical paths**: Protected in FP32 for stability

## Usage Examples

### Basic FP16 Training
```bash
python examples/model_train_multi.py \
    --train-config configs/multidataset_train_config.yaml \
    --fp16 \
    --max-epochs 50 \
    --batch-size 256
```

### FP16 with Compilation
```bash
python examples/model_train_multi.py \
    --train-config configs/multidataset_train_config.yaml \
    --fp16 \
    --compile \
    --max-epochs 50
```

### Test Mode with FP16
```bash
python examples/model_train_multi.py \
    --train-config configs/multidataset_train_config.yaml \
    --fp16 \
    --test \
    --max-epochs 1
```

## Monitoring and Debugging

### Gradient Monitoring
The `GradientMonitorCallback` logs:
- Gradient norms every 100 steps
- NaN/Inf gradient detection
- Gradient scaler state
- Automatic warnings for numerical issues

### WandB Logging
Additional metrics logged during FP16 training:
- `grad_norm`: Overall gradient norm
- `grad_scale`: Current gradient scaler value
- Standard training/validation metrics

### Debugging Tips

1. **Check for NaN/Inf**: Monitor console output for gradient warnings
2. **Gradient Scaling**: Watch `grad_scale` in WandB - should be stable
3. **Loss Stability**: Compare FP16 vs FP32 loss curves
4. **Memory Usage**: Monitor GPU memory usage reduction

## Troubleshooting

### Common Issues

1. **Gradient Underflow**
   - Symptoms: Training stalls, very small gradients
   - Solution: Reduce learning rate further or check model architecture

2. **Loss Instability**
   - Symptoms: Erratic loss curves, NaN losses
   - Solution: Verify all critical components are in FP32

3. **Memory Issues**
   - Symptoms: OOM errors despite FP16
   - Solution: Reduce batch size or check for memory leaks

### Performance Expectations

- **Memory Reduction**: 40-50% typical reduction
- **Speed Improvement**: 1.2-1.8x speedup on modern GPUs
- **Numerical Stability**: Should match FP32 training closely

## Advanced Configuration

### Custom Learning Rate Scaling
The implementation automatically reduces learning rate for FP16. To customize:

```python
# In training script
base_lr = 5e-4
if use_fp16:
    base_lr = 1e-4  # More conservative
```

### Gradient Clipping Adjustment
Default gradient clipping is set to 1.0. To adjust:

```python
trainer_kwargs["gradient_clip_val"] = 0.5  # More aggressive clipping
```

## Best Practices

1. **Start Conservative**: Begin with reduced learning rates
2. **Monitor Closely**: Watch for gradient issues in first few epochs
3. **Compare Baselines**: Validate against FP32 training initially
4. **Test Incrementally**: Enable FP16 on smaller models first
5. **Use Compilation**: Combine with `--compile` for maximum performance

## Implementation Notes

- Compatible with both single and multidataset training
- Preserves all existing functionality
- Minimal code changes required for new models
- Automatic fallback to FP32 for critical operations
