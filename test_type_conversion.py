#!/usr/bin/env python
"""
Test script to verify the type conversion in config merging works correctly.
"""

import sys
import argparse
from pathlib import Path
import yaml

# Add the project root to the path
sys.path.insert(0, str(Path(__file__).parent))

def create_test_config_with_scientific_notation():
    """Create a test config file with scientific notation that might be parsed as strings."""
    test_config = {
        'training_type': 'v1multi',
        'model_config': 'test_model.yaml',
        'datasets': [
            {'path': 'test_dataset.yaml'}
        ],
        'training': {
            'max_epochs': 50,
            'batch_size': 128,
            'early_stopping_patience': 10,
            'fp16': True,
            'accumulate_grad_batches': 2,
            'compile': True,
            'debug': False
        },
        'optimizer': {
            'type': 'AdamW',
            'lr': '5e-4',  # Deliberately as string to test conversion
            'weight_decay': '1e-5'  # Deliberately as string to test conversion
        }
    }
    
    test_config_path = Path('test_scientific_notation_config.yaml')
    with open(test_config_path, 'w') as f:
        yaml.dump(test_config, f, default_flow_style=False)
    
    return test_config_path

def test_type_conversion():
    """Test the type conversion functionality."""
    print("Testing type conversion in config merging...")
    
    # Create test config with string scientific notation
    test_config_path = create_test_config_with_scientific_notation()
    
    try:
        # Import the merge function
        from examples.model_train_multi import merge_config_with_args
        
        # Create mock args with some values set and others None
        class MockArgs:
            def __init__(self):
                self.max_epochs = None  # Use config value
                self.batch_size = None  # Use config value
                self.lr = None  # Use config value (string scientific notation)
                self.weight_decay = None  # Use config value (string scientific notation)
                self.optimizer = None  # Use config value
                self.fp16 = None  # Use config value
                self.accumulate_grad_batches = None  # Use config value
                self.compile = None  # Use config value
                self.debug = None  # Use config value
                self.early_stopping_patience = None  # Use config value
                self.device = None
                self.tensor_core_precision = None
                self.num_workers = None
                self.fp16_opt_level = None
                self.fp16_conservative = None
                self.fp16_init_scale = None
                self.debug_dataloader = None
                self.skip_sanity_check = None
                self.overflow_threshold = None
                self.disable_overflow_hooks = None
                self.test = None
                self.no_codename = None
        
        args = MockArgs()
        
        # Test the merge function
        merged_config = merge_config_with_args(test_config_path, args)
        
        print("\n=== MERGED CONFIGURATION WITH TYPE CONVERSION ===")
        for key, value in merged_config.items():
            print(f"{key}: {value} (type: {type(value).__name__})")
        
        # Verify expected types and values
        print("\n=== TYPE CONVERSION VERIFICATION ===")
        
        # Check that lr is converted to float
        lr_value = merged_config['lr']
        assert isinstance(lr_value, float), f"lr should be float, got {type(lr_value)}"
        assert lr_value == 5e-4, f"lr should be 5e-4, got {lr_value}"
        print(f"✓ lr correctly converted: {lr_value} (type: {type(lr_value).__name__})")
        
        # Check that weight_decay is converted to float
        wd_value = merged_config['weight_decay']
        assert isinstance(wd_value, float), f"weight_decay should be float, got {type(wd_value)}"
        assert wd_value == 1e-5, f"weight_decay should be 1e-5, got {wd_value}"
        print(f"✓ weight_decay correctly converted: {wd_value} (type: {type(wd_value).__name__})")
        
        # Check that integers are properly converted
        max_epochs = merged_config['max_epochs']
        assert isinstance(max_epochs, int), f"max_epochs should be int, got {type(max_epochs)}"
        print(f"✓ max_epochs correctly converted: {max_epochs} (type: {type(max_epochs).__name__})")
        
        # Check that booleans are properly converted
        use_fp16 = merged_config['use_fp16']
        assert isinstance(use_fp16, bool), f"use_fp16 should be bool, got {type(use_fp16)}"
        print(f"✓ use_fp16 correctly converted: {use_fp16} (type: {type(use_fp16).__name__})")
        
        # Test that the values would work with PyTorch optimizer
        print("\n=== PYTORCH OPTIMIZER COMPATIBILITY TEST ===")
        try:
            # Simulate what PyTorch optimizer does
            lr_check = 0.0 <= lr_value  # This was failing before
            wd_check = 0.0 <= wd_value
            print(f"✓ lr comparison works: 0.0 <= {lr_value} = {lr_check}")
            print(f"✓ weight_decay comparison works: 0.0 <= {wd_value} = {wd_check}")
        except TypeError as e:
            print(f"❌ PyTorch compatibility test failed: {e}")
            raise
        
        print("\n✅ All type conversion tests passed! Scientific notation strings are properly converted to floats.")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Clean up test file
        if test_config_path.exists():
            test_config_path.unlink()

if __name__ == "__main__":
    test_type_conversion()
