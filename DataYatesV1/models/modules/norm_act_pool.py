# norm_act_pool.py
import torch
import torch.nn as nn
from typing import Optional, Dict, Any, Union, Tuple

from .common import SplitRelu # Assuming common.py is in the same directory level

__all__ = ['get_norm_layer', 'get_activation_layer', 'get_pooling_layer', 'RMSNorm', 'LayerNorm']


class RMSNorm(nn.Module):
    def __init__(self, norm_dims: tuple = (1,), eps: float = 1e-4):
        """
        Custom Root Mean Square Normalization (no learnable parameters)

        Args:
            norm_dims (tuple): A tuple of dimension indices over which the mean
                               of squares will be computed. These are the
                               dimensions that get normalized.
                               E.g., `(-1,)` for the last dimension. 
                               Defaults to the channel dimension: (1,)
            eps (float): A small value added to the denominator for
                         numerical stability.
        """
        super().__init__()
        if not isinstance(norm_dims, tuple):
            raise TypeError("norm_dims must be a tuple of dimension indices.")
        self.norm_dims = norm_dims
        self.eps = eps

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # Ensure computation is in FP32 for numerical stability
        input_dtype = x.dtype
        x_fp32 = x.float()

        mean_of_squares = torch.mean(x_fp32.pow(2), dim=self.norm_dims, keepdim=True)
        normalized_x = x_fp32 * torch.rsqrt(mean_of_squares + self.eps)

        # Convert back to input dtype
        return normalized_x.to(input_dtype)

    def extra_repr(self) -> str:
        return f"norm_dims={self.norm_dims}, eps={self.eps}"
    
class LayerNorm(nn.Module):
    def __init__(self, norm_dims: tuple = (1,), eps: float = 1e-4):
        """
        Custom Layer Norm (no learnable parameters).

        Args:
            norm_dims (tuple): A tuple of dimension indices over which the mean
                               and variance will be computed. These are the
                               dimensions that get normalized.
                               E.g., `(-1,)` for the last dimension.
                               Defaults to the channel dimension: (1,)
            eps (float): A small value added to the denominator for
                         numerical stability.
        """
        super().__init__()
        if not isinstance(norm_dims, tuple):
            raise TypeError("norm_dims must be a tuple of dimension indices.")
        self.norm_dims = norm_dims
        self.eps = eps

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # Ensure computation is in FP32 for numerical stability
        input_dtype = x.dtype
        x_fp32 = x.float()

        # Calculate mean and variance over the specified self.norm_dims
        mean = torch.mean(x_fp32, dim=self.norm_dims, keepdim=True)
        variance = torch.mean(x_fp32.pow(2), dim=self.norm_dims, keepdim=True) - mean.pow(2)
        normalized_x = (x_fp32 - mean) * torch.rsqrt(variance + self.eps)

        # Convert back to input dtype
        return normalized_x.to(input_dtype)

    def extra_repr(self) -> str:
        return f"norm_dims={self.norm_dims}, eps={self.eps}"
    
class GlobalResponseNorm(nn.Module):
    def __init__(self, C, eps=1e-4, gamma_init=0.1, clamp_ratio=50.):
        super().__init__()
        self.gamma = nn.Parameter(torch.full((1, C, 1, 1, 1), gamma_init))
        self.beta  = nn.Parameter(torch.zeros(1, C, 1, 1, 1))
        self.eps   = eps
        self.clamp = clamp_ratio          # ≥ 1  (set None to disable)

    def forward(self, x):
        # Ensure computation is in FP32 for numerical stability
        input_dtype = x.dtype
        x_fp32 = x.float()
        gamma_fp32 = self.gamma.float()
        beta_fp32 = self.beta.float()

        g   = torch.norm(x_fp32, p=2, dim=1, keepdim=True)
        mu  = g.mean((2, 3, 4), keepdim=True)
        r   = g / (mu + self.eps)
        if self.clamp is not None:                       # guard FP16 range
            r = torch.clamp(r, max=self.clamp)
        result = x_fp32 + gamma_fp32 * (x_fp32 * r) + beta_fp32

        # Convert back to input dtype
        return result.to(input_dtype)

def get_norm_layer(norm_type: Optional[str],
                   num_features: int,
                   # `dim` here refers to the type of operation (1D, 2D, 3D for BatchNorm/InstanceNorm)
                   # For LayerNorm/RMSNorm, 'op_dim' is the targeted dimension(s) int or Tuple[int]
                   op_dim: Union[int, Tuple[int, ...]], # 1, 2, or 3 for BatchNorm/InstanceNorm type
                   norm_params: Optional[Dict[str, Any]] = None) -> nn.Module:
    """Factory for normalization layers.
    Can be:
    - 'batch', 'instance', 'layer', 'rms', 'group', or 'none'
    """
    if norm_type is None or norm_type.lower() == 'none': return nn.Identity()
    if norm_params is None: norm_params = {}

    norm_type_lower = norm_type.lower()
    eps = norm_params.get('eps', 1e-5)
    target_dim = norm_params.get('target_dim', 1) # Default to channel dimension
    affine = norm_params.get('affine', True) # Default affine=True for most, False for InstanceNorm by default

    if norm_type_lower == 'batch':
        if op_dim == 1: return nn.BatchNorm1d(num_features, eps=eps, affine=affine)
        if op_dim == 2: return nn.BatchNorm2d(num_features, eps=eps, affine=affine)
        if op_dim == 3: return nn.BatchNorm3d(num_features, eps=eps, affine=affine)
        raise ValueError(f"Unsupported op_dim {op_dim} for BatchNorm.")

    elif norm_type_lower == 'instance':
        # PyTorch InstanceNorm default affine is False
        instance_affine = norm_params.get('affine', False)
        if op_dim == 1: return nn.InstanceNorm1d(num_features, eps=eps, affine=instance_affine)
        if op_dim == 2: return nn.InstanceNorm2d(num_features, eps=eps, affine=instance_affine)
        if op_dim == 3: return nn.InstanceNorm3d(num_features, eps=eps, affine=instance_affine)
        raise ValueError(f"Unsupported op_dim {op_dim} for InstanceNorm.")

    elif norm_type_lower == 'layer':
        if isinstance(target_dim, int): target_dim = (target_dim,)
        return LayerNorm(target_dim, eps=eps) # custom Layer norm with no learned parameters

    elif norm_type_lower == 'rms':
        if isinstance(target_dim, int): target_dim = (target_dim,)
        return RMSNorm(target_dim, eps=eps) # custom RMS norm with no learned parameters

    elif norm_type_lower == 'group':
        num_groups = norm_params.get('num_groups', 1) # Default to 1 (LayerNorm-like over spatial)
        if num_features > 0 and num_features % num_groups != 0:
             # Fallback if not divisible, or user should ensure it is.
             print(f"Warning: GroupNorm num_features ({num_features}) not divisible by num_groups ({num_groups}). Using num_groups=1.")
             num_groups = 1
        elif num_features == 0 and num_groups > 1 : # Edge case
             num_groups = 1

        return nn.GroupNorm(num_groups, num_features, eps=eps, affine=affine)
    
    elif norm_type_lower == 'grn':
        return GlobalResponseNorm(num_features, eps=eps)
    
    else:
        raise ValueError(f"Unknown normalization type: '{norm_type}'.")


def get_activation_layer(act_type: Optional[str],
                         act_params: Optional[Dict[str, Any]] = None) -> nn.Module:
    """Factory for activation layers.
    Can be:
    - 'relu', 'leakyrelu', 'gelu', 'sigmoid', 'tanh', 'silu', 'swish', 'mish', 'splitrelu'
    - Any other activation in torch.nn can be specified by name
    - If None or 'none', returns nn.Identity()
    """
    
    if act_type is None or act_type.lower() == 'none': return nn.Identity()
    if act_params is None: act_params = {}

    act_type_lower = act_type.lower()
    inplace = act_params.get('inplace', False) # Common param

    if act_type_lower == 'relu': return nn.ReLU(inplace=inplace)
    if act_type_lower == 'leakyrelu': return nn.LeakyReLU(act_params.get('negative_slope', 0.01), inplace=inplace)
    if act_type_lower == 'gelu': return nn.GELU(approximate=act_params.get('approximate', 'none'))
    if act_type_lower == 'sigmoid': return nn.Sigmoid()
    if act_type_lower == 'tanh': return nn.Tanh()
    if act_type_lower in ['silu', 'swish']: return nn.SiLU(inplace=inplace)
    if act_type_lower == 'mish': return nn.Mish(inplace=inplace) # Assumes PyTorch >= 2.0
    if act_type_lower == 'softplus': return nn.Softplus()
    if act_type_lower == 'none': return nn.Identity()
    if act_type_lower == 'square': return lambda x: x**2
    if act_type_lower == 'splitrelu':
        return SplitRelu(split_dim=act_params.get('split_dim', 1),
                         trainable_gain=act_params.get('trainable_gain', False))

    # Attempt to get from torch.nn for other activations
    try:
        ActivationClass = getattr(nn, act_type) # Case-sensitive
        # Simple instantiation, pass inplace if it's a known arg for that class
        # More complex arg passing would require inspect, but keep it simple
        if 'inplace' in ActivationClass.__init__.__code__.co_varnames:
            return ActivationClass(inplace=inplace)
        return ActivationClass()
    except AttributeError:
        raise ValueError(f"Unknown activation type: '{act_type}'.")


def get_pooling_layer(pool_params: Optional[Dict[str, Any]] = None, op_dim: int = 2) -> nn.Module:
    """Factory for pooling layers.
    Can be:
    - 'max', 'avg', 'adaptivemax', 'adaptiveavg', or 'none'
    """
    if pool_params is None or pool_params.get('type', 'none').lower() == 'none':
        return nn.Identity()

    pool_type = pool_params['type'].lower()
    kernel_size = pool_params['kernel_size']
    stride = pool_params.get('stride', kernel_size) # Default stride to kernel_size
    padding = pool_params.get('padding', 0)

    PoolNd = getattr(nn, f"MaxPool{op_dim}d", None) if pool_type == 'max' else \
             getattr(nn, f"AvgPool{op_dim}d", None) if pool_type == 'avg' else \
             getattr(nn, f"AdaptiveMaxPool{op_dim}d", None) if pool_type == 'adaptivemax' else \
             getattr(nn, f"AdaptiveAvgPool{op_dim}d", None) if pool_type == 'adaptiveavg' else None

    if PoolNd is None: raise ValueError(f"Unsupported pool type '{pool_type}' or op_dim {op_dim}.")

    if 'adaptive' in pool_type:
        return PoolNd(output_size=kernel_size) # kernel_size is output_size for adaptive
    return PoolNd(kernel_size=kernel_size, stride=stride, padding=padding)

