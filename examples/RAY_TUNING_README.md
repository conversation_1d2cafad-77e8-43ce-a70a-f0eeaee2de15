# Ray Hyperparameter Tuning for Neural Network Models

This directory contains a comprehensive hyperparameter tuning pipeline using Ray Tune and Optuna for training neural network models across multiple datasets.

## Overview

The pipeline provides:
- **Automated hyperparameter optimization** using Ray Tune + Optuna
- **Multiple architecture support** (ResNet, DenseNet, X3D)
- **Batch processing** across all available datasets
- **Robust experiment management** with resumable training
- **Integration with model registry** for tracking results
- **Week-long experiment support** with proper checkpointing

## Key Files

- `model_train_ray.py` - Core hyperparameter tuning script
- `run_massive_ray_tuning.py` - Batch processing wrapper
- `configs/ray_tuning_config.yaml` - Configuration template
- `RAY_TUNING_README.md` - This documentation

## Quick Start

### 1. Single Dataset Tuning

```bash
# Basic usage
python model_train_ray.py \
    --dataset-config /path/to/dataset.yaml \
    --model-config /path/to/model.yaml \
    --num-trials 100

# With custom settings
python model_train_ray.py \
    --dataset-config /mnt/ssd/YatesMarmoV1/conv_model_fits/data_configs/multi_dataset_temporal_basis/Allen_2022-04-13.yaml \
    --model-config /home/<USER>/repos/DataYatesV1/examples/configs/test_model_stimembed_3dconv_big_multi.yaml \
    --num-trials 200 \
    --max-epochs-per-trial 30 \
    --experiment-name "allen_0413_deep_search"
```

### 2. Batch Processing (All Datasets)

```bash
# Process all datasets with default settings
python run_massive_ray_tuning.py --all-datasets

# Custom batch processing
python run_massive_ray_tuning.py \
    --all-datasets \
    --num-trials 150 \
    --max-epochs 30

# Test run (reduced trials/epochs)
python run_massive_ray_tuning.py \
    --all-datasets \
    --test-mode
```

### 3. Information and Planning

```bash
# List available datasets
python run_massive_ray_tuning.py --list-datasets

# Estimate runtime
python run_massive_ray_tuning.py \
    --all-datasets \
    --num-trials 100 \
    --estimate-time
```

## Hyperparameters Tuned

### Optimizer Parameters
- **Learning rate**: 1e-5 to 1e-2 (log-uniform)
- **Weight decay**: 1e-6 to 1e-3 (log-uniform)

### Training Parameters
- **Batch size**: [128, 256, 512]

### Architecture Selection
- **Convnet type**: [resnet, densenet, x3d]

### Architecture-Specific Parameters

**ResNet:**
- Depth: [18, 34, 50]
- Width multiplier: [1.0, 1.5, 2.0]
- Stem channels: [32, 64, 128]

**DenseNet:**
- Growth rate: [12, 24, 32]
- Number of layers: [121, 169, 201]
- Compression: 0.3 to 0.7 (uniform)

**X3D:**
- Variant: [xs, s, m]
- Width multiplier: [1.0, 1.5, 2.0]

## Experiment Management

### Directory Structure
```
/mnt/ssd/YatesMarmoV1/conv_model_fits/
├── ray_experiments/
│   ├── experiment_name/
│   │   ├── trial_*/
│   │   ├── experiment_summary.yaml
│   │   └── analysis_results/
│   └── experiment_summary_timestamp.yaml
├── runs/
│   └── ray_tuned_models/
└── model_registry_ray/
    └── registered_models.json
```

### Resuming Experiments
```bash
# Resume interrupted experiment
python model_train_ray.py \
    --dataset-config /path/to/dataset.yaml \
    --model-config /path/to/model.yaml \
    --resume \
    --experiment-name "previous_experiment_name"
```

## Resource Management

### GPU/CPU Allocation
```bash
# Specify resources explicitly
python model_train_ray.py \
    --dataset-config /path/to/dataset.yaml \
    --model-config /path/to/model.yaml \
    --num-gpus 4 \
    --num-cpus 32
```

### Ray Configuration
The pipeline automatically configures Ray with:
- **CPU allocation**: 2 CPUs per trial
- **GPU allocation**: 0.5 GPU per trial (shared)
- **Memory management**: Conservative settings for stability

## Advanced Usage

### Custom Search Spaces
Modify the `get_search_space()` function in `model_train_ray.py` to add new hyperparameters:

```python
search_space.update({
    'custom_param': tune.uniform(0.1, 1.0),
    'custom_choice': tune.choice(['option1', 'option2'])
})
```

### Custom Model Configurations
Create new model configs and pass them to the script:

```bash
python model_train_ray.py \
    --dataset-config /path/to/dataset.yaml \
    --model-config /path/to/custom_model.yaml
```

## Monitoring and Results

### Weights & Biases Integration
Results are automatically logged to W&B:
- Project: "Digital Twin Ray Tuning"
- Entity: "yateslab"
- Metrics: validation loss, training loss, hyperparameters

### Model Registry
Best models are automatically registered with metadata:
- Experiment name and timestamp
- Best hyperparameters
- Session and dataset information
- Performance metrics

### Analysis Results
Each experiment generates:
- `experiment_summary.yaml` - Complete experiment metadata
- Best trial configuration
- Performance comparison across trials
- Final model checkpoint path

## Troubleshooting

### Common Issues

1. **Out of Memory Errors**
   - Reduce batch size in search space
   - Decrease `gpus_per_trial` to 0.25
   - Use fewer workers: `--num-workers 1`

2. **Ray Initialization Failures**
   - Check available resources: `ray status`
   - Restart Ray: `ray stop && ray start --head`

3. **Dataset Loading Issues**
   - Verify dataset paths exist
   - Check dataset configuration format
   - Ensure sufficient disk space

### Performance Tips

1. **For Long Experiments**
   - Use aggressive early stopping (patience=3-5)
   - Start with fewer trials to test setup
   - Monitor disk space for checkpoints

2. **For Resource-Constrained Systems**
   - Reduce concurrent trials
   - Use smaller batch sizes
   - Disable model compilation

## Example Workflows

### 1. Quick Baseline Establishment
```bash
# Fast baseline for all datasets
python run_massive_ray_tuning.py \
    --all-datasets \
    --num-trials 50 \
    --max-epochs 15 \
    --test-mode
```

### 2. Deep Search on Important Datasets
```bash
# Thorough search on key sessions
for dataset in Allen_2022-04-13.yaml Logan_2020-01-15.yaml; do
    python model_train_ray.py \
        --dataset-config "/mnt/ssd/YatesMarmoV1/conv_model_fits/data_configs/multi_dataset_temporal_basis/$dataset" \
        --model-config /path/to/model.yaml \
        --num-trials 300 \
        --max-epochs-per-trial 40
done
```

### 3. Architecture Comparison
```bash
# Compare architectures on specific dataset
python model_train_ray.py \
    --dataset-config /path/to/dataset.yaml \
    --model-config /path/to/model.yaml \
    --num-trials 200 \
    --experiment-name "architecture_comparison"
```

## Expected Runtime

For reference, typical runtimes:
- **Single dataset, 100 trials**: 6-12 hours
- **All datasets (~30), 100 trials each**: 7-14 days
- **Test mode (10 trials, 5 epochs)**: 1-2 hours per dataset

Runtime depends on:
- Number of GPUs available
- Dataset size and complexity
- Early stopping effectiveness
- Architecture complexity
