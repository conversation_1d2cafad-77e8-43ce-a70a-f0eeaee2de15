# Multidataset training configuration
# This config specifies the model and multiple datasets for training

training_type: v1multi

# Path to the main model configuration (shared components)
model_config: "/home/<USER>/repos/DataYatesV1/examples/configs/test_model_stimembed_3dconv_big_multi.yaml"

# List of dataset configurations
datasets:
  - path: "/home/<USER>/repos/DataYatesV1/examples/configs/test_data_stimembed_symlog_5lags.yaml"

# Training-specific parameters
training:
  accumulate_grad_batches: 1
  max_epochs: 100
  batch_size: 256
  early_stopping_patience: 5
  fp16: false
  
# Optimizer configuration
optimizer:
  type: AdamW
  lr: 5e-4
  weight_decay: 1e-5
  eps: 1e-8

# Scheduler configuration (optional)
scheduler:
  type: cosine
  warmup_epochs: 5
