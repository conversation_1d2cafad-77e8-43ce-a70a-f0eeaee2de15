# Configuration for Ray hyperparameter tuning experiments
# This file defines the search spaces and experiment settings

# Experiment metadata
experiment:
  name: "massive_v1_hyperparameter_search"
  description: "Comprehensive hyperparameter tuning across all V1 datasets"
  version: "1.0"

# Resource allocation
resources:
  # Ray cluster settings
  num_cpus: null  # auto-detect
  num_gpus: null  # auto-detect
  
  # Per-trial resource allocation
  cpus_per_trial: 2
  gpus_per_trial: 0.5  # Share GPUs between trials
  
  # Memory management
  object_store_memory: "4GB"
  
# Search algorithm configuration
search:
  algorithm: "optuna"  # optuna, hyperopt, or random
  
  # Optuna-specific settings
  optuna:
    sampler: "TPE"  # TPE, Random, or CmaEs
    n_startup_trials: 20  # Number of random trials before TPE
    multivariate: true
    seed: 42
    
  # Early stopping scheduler
  scheduler:
    type: "ASHA"  # ASHA, MedianStopping, or HyperBand
    metric: "val_loss"
    mode: "min"
    grace_period: 5  # Minimum epochs before stopping
    reduction_factor: 3
    max_t: 25  # Maximum epochs per trial

# Hyperparameter search spaces
hyperparameters:
  # Optimizer parameters
  optimizer:
    lr:
      type: "loguniform"
      low: 1e-5
      high: 1e-2
      
    weight_decay:
      type: "loguniform" 
      low: 1e-6
      high: 1e-3
      
  # Training parameters
  training:
    batch_size:
      type: "choice"
      choices: [128, 256, 512]
      
  # Architecture selection
  architecture:
    convnet_type:
      type: "choice"
      choices: ["resnet", "densenet", "x3d"]
      
    # ResNet-specific parameters
    resnet:
      depth:
        type: "choice"
        choices: [18, 34, 50]
        
      width_multiplier:
        type: "choice"
        choices: [1.0, 1.5, 2.0]
        
      stem_channels:
        type: "choice"
        choices: [32, 64, 128]
        
    # DenseNet-specific parameters  
    densenet:
      growth_rate:
        type: "choice"
        choices: [12, 24, 32]
        
      num_layers:
        type: "choice"
        choices: [121, 169, 201]
        
      compression:
        type: "uniform"
        low: 0.3
        high: 0.7
        
    # X3D-specific parameters
    x3d:
      variant:
        type: "choice"
        choices: ["xs", "s", "m"]
        
      width_multiplier:
        type: "choice"
        choices: [1.0, 1.5, 2.0]

# Training configuration
training:
  max_epochs_per_trial: 25
  max_epochs_final_model: 50
  early_stopping_patience: 5
  final_model_patience: 10
  
  # Validation frequency
  check_val_every_n_epoch: 1
  
  # Logging
  log_every_n_steps: 50
  
# Experiment management
experiment_management:
  # Checkpoint settings
  save_top_k: 1
  save_last: true
  
  # Failure handling
  max_failures_per_trial: 3
  max_total_failures: 100  # 10% of 1000 trials
  
  # Resume settings
  resume_from_checkpoint: true
  checkpoint_frequency: 10  # Save experiment state every 10 trials
  
# Output configuration
output:
  # Base directory for all experiments
  base_dir: "/mnt/ssd/YatesMarmoV1/conv_model_fits/ray_experiments"
  
  # Subdirectories
  checkpoints_dir: "checkpoints"
  logs_dir: "logs"
  results_dir: "results"
  
  # File formats
  save_analysis_as: ["csv", "json", "pickle"]
  
  # Visualization
  create_plots: true
  plot_formats: ["png", "pdf"]

# Logging and monitoring
logging:
  # Weights & Biases integration
  wandb:
    enabled: true
    project: "Digital Twin Ray Tuning"
    entity: "yateslab"
    tags: ["hyperparameter_tuning", "ray", "optuna"]
    
  # Ray dashboard
  ray_dashboard: false  # Disable for cluster runs
  
  # Progress reporting
  verbose: 1  # 0=silent, 1=minimal, 2=verbose
  
# Dataset-specific overrides
dataset_overrides:
  # Example: different settings for specific sessions
  "Allen_2022-04-13":
    training:
      max_epochs_per_trial: 30  # More epochs for this dataset
      
  "Logan_2020-01-15":
    hyperparameters:
      training:
        batch_size:
          type: "choice"
          choices: [64, 128, 256]  # Smaller batches for this dataset

# Model registry configuration
model_registry:
  enabled: true
  registry_dir: "/mnt/ssd/YatesMarmoV1/conv_model_fits/model_registry_ray"
  
  # Metadata to store
  metadata_fields:
    - "experiment_name"
    - "session"
    - "num_units" 
    - "best_hyperparameters"
    - "ray_tuned"
    - "total_trials"
    - "best_trial_id"
    
# Performance optimization
performance:
  # PyTorch settings
  torch_compile: false  # Disable for Ray trials (can cause issues)
  mixed_precision: false  # Disable FP16 for stability
  
  # DataLoader settings
  num_workers: 2  # Conservative for Ray
  pin_memory: true
  persistent_workers: false
  
  # CUDA settings
  cuda_benchmark: true
  cuda_deterministic: false  # Allow non-deterministic for speed

# Debugging and testing
debug:
  # Test mode settings
  test_mode:
    num_trials: 10
    max_epochs_per_trial: 5
    datasets_limit: 3  # Only test on first 3 datasets
    
  # Debug options
  detect_anomaly: false
  profile_memory: false
  
  # Failure debugging
  save_failed_trials: true
  debug_failed_trials: false
