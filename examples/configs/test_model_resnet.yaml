# V1 multidataset model configuration
# This model uses adapter frontends (per dataset) -> shared DenseNet -> shared recurrent -> readouts (per dataset)
model_type: v1multi

# Model dimensions
sampling_rate: 240
initial_input_channels: 6 # must match num cosines in stim embed

# Note: Frontend configurations are specified in individual dataset configs
# All frontends must be of type 'adapter' for multidataset training

# Shared convnet configuration
convnet:
  type: resnet
  params:
      channels: [32, 64, 128]  # Each layer outputs these many channels
      dim: 3
      checkpointing: true
      block_config:
        conv_params:
          type: standard
          kernel_size: [3, 3, 3]
          padding: [0,0,0]
        norm_type: batch
        act_type: silu
        pool_params: {}

# Shared modulator configuration
modulator:
  type: film
  params:
    behavior_dim: 40
    feature_dim: 128  # Number of convnet output channels (matches convnet final stage)
    encoder_params:
      type: mlp
      dims: [128, 128]  # Hidden layers + output size
      activation: gelu
      bias: true
      residual: true
      dropout: 0.1
      last_layer_activation: true

# Shared recurrent configuration
recurrent:
  type: none
  params: {}

# Note: Readout configurations are specified in individual dataset configs
# Each dataset will have its own readout with n_units = len(cids)

output_activation: none