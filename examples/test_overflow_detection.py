#!/usr/bin/env python
"""
Test script to demonstrate overflow detection during FP16 training.

This script shows how to use the overflow detection hooks to debug inf/NaN issues
in your model during mixed precision training.

Usage:
    # Run with overflow detection enabled (default with FP16)
    python test_overflow_detection.py --fp16 --test

    # Run with custom overflow threshold
    python test_overflow_detection.py --fp16 --test --overflow-threshold 1e3

    # Run with overflow detection disabled
    python test_overflow_detection.py --fp16 --test --disable-overflow-hooks

    # Run in debug mode (enables overflow detection even without FP16)
    python test_overflow_detection.py --debug --test

Example output when overflow is detected:
    [OVERFLOW] convnet.layers.0.conv: max|x|=1.23e+05
    [OVERFLOW] readouts.0.spatial.weight_sampler.grid: max|x|=inf
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from model_train_multi import train_multidataset_model

def main():
    """
    Example of running training with overflow detection.
    
    The overflow detection will automatically be enabled when:
    1. Using FP16 training (--fp16 flag)
    2. Using debug mode (--debug flag)
    
    You can customize the detection threshold with --overflow-threshold
    or disable it entirely with --disable-overflow-hooks
    """
    
    # Example training config path - adjust this to your actual config
    train_config_path = "examples/configs/multidataset_train_config.yaml"
    
    print("🔍 Testing overflow detection during training...")
    print(f"Using config: {train_config_path}")
    print()
    print("The overflow detection hooks will:")
    print("1. Monitor all leaf modules in your model")
    print("2. Check outputs for values above threshold or inf/NaN")
    print("3. Print warnings when overflow is detected")
    print("4. Help you identify which layer is causing the problem")
    print()
    
    try:
        # Run training with overflow detection
        best_model_path = train_multidataset_model(
            train_config_path=train_config_path,
            model_name="overflow_test",
            device="auto",
            max_epochs=1,  # Just one epoch for testing
            compile=False,
            test_mode=True,  # Limit batches for quick testing
            early_stopping_patience=5,
            batch_size=64,  # Smaller batch for testing
            use_fp16=True,  # Enable FP16 to trigger overflow detection
            skip_sanity_check=True,  # Skip sanity check for faster testing
            debug_mode=False,
            overflow_threshold=5e4,  # Default threshold
            disable_overflow_hooks=False  # Enable overflow detection
        )
        
        print(f"\n✅ Training completed successfully!")
        print(f"Best model saved to: {best_model_path}")
        
    except FileNotFoundError as e:
        print(f"❌ Config file not found: {e}")
        print("Please adjust the train_config_path in this script to point to a valid config file.")
        print("Available configs in examples/configs/:")
        import glob
        configs = glob.glob("examples/configs/*.yaml")
        for config in configs:
            print(f"  - {config}")
            
    except Exception as e:
        print(f"❌ Training failed: {e}")
        print("This might be due to overflow issues that the hooks detected!")

if __name__ == "__main__":
    # You can also run this with command line arguments by importing argparse
    # and using the same argument parsing as model_train_multi.py
    main()
