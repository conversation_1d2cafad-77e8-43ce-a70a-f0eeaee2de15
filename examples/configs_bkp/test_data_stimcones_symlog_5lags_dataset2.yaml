# Dataset 2 configuration for multidataset training
# This is a copy of the original dataset config but with different cids and session

types: [gaborium, backimage]
keys_lags:
  robs: 0
  stim: [0, 1, 2, 3, 4]
  behavior: 0 # will match the `expose_as` in `transforms`
  dfs: 0

# Frontend configuration (must be adapter for multidataset training)
frontend:
  type: adapter
  params:
    init_sigma: 1.2
    grid_size: 25
    transform: scale

# Readout configuration (dataset-specific)
readout:
  type: gaussian
  params:
    bias: true
    initial_std: 5.0
    # n_units will be set automatically based on len(cids)

transforms:
  stim:
    source: stim
    ops:
      - pixelnorm: {}
      - dacones:     # acausal raised-cosine embedding
          alpha: 0.01
          beta: 0.00008
          gamma: 0.5
          tau_y_ms: 5.0
          tau_z_ms: 60.0
          n_y: 5.0
          n_z: 2.0
          filter_length: 32
          learnable_params: false
    expose_as: stim

  eye_vel:
    source: eyepos          # raw key in DictDataset
    ops:                    # ordered transform list
      - diff: {axis: 0} # finite-difference velocity
      # - mul: 240            # Hz → deg s⁻¹
      - maxnorm: {}
      - symlog: {}
      - temporal_basis:     # acausal raised-cosine embedding
          num_delta_funcs: 0
          num_cosine_funcs: 10
          history_bins: 50
          causal: false
          log_spacing: false
          peak_range_ms: [30, 200]
          normalize: true
      - splitrelu:
          split_dim: 1
          trainable_gain: false
    expose_as: behavior     # place in batch["behavior"]

datafilters:
  dfs:
    ops:
      - valid_nlags: {n_lags: 32}
    expose_as: dfs
    
train_val_split: 0.8
# Different cids for dataset 2 (subset of original for testing)
cids: [6, 10, 15, 17, 18, 19, 20, 22, 23, 25, 27, 28, 29, 30]
seed: 1003  # Different seed
session: Allen_2022-04-13  # Same session for now, but could be different
