# V1 model configuration with eye position modulation
# This model uses a DA frontend -> DenseNet -> flattened -> GRU -> linear readout
# with MLP modulator output concatenated with the flattened DenseNet output
model_type: v1

# Model dimensions
sampling_rate: 240
initial_input_channels: 5 # must match num cosines in stim embed

# Frontend configuration
frontend:
  type: none
  params: {}

# Convnet configuration
convnet:
  type: densenet
  params:
    growth_rate: 12
    num_blocks: 3
    dim: 3
    checkpointing: true
    block_config:
      conv_params:
        type: depthwise
        dim: 3
        kernel_size: [2, 5, 5]
        padding: [0,0,0] # or what?
      norm_type: batch
      act_type: gelu
      pool_params:
        type: avg
        kernel_size: [1, 2, 2]  # Pool only in spatial dimensions
        stride: [1, 2, 2]

# Modulator configuration
modulator:
  type: film
  params:
    behavior_dim: 40
    feature_dim: 32  # Number of convnet output channels
    encoder_params:
      type: mlp
      dims: [128, 128]  # Hidden layers + output size
      activation: gelu
      bias: true
      residual: true
      dropout: 0.1
      last_layer_activation: true

# # Recurrent configuration
# recurrent:
#   type: convgru
#   params:
#     hidden_dim: 128
#     kernel_size: 3

# Recurrent configuration
recurrent:
  type: none
  params: {}

# Readout configuration
readout:
  type: gaussian
  params:
    n_units: 8
    bias: true
    initial_std: 5.0