#!/usr/bin/env python
"""
Massive hyperparameter tuning script for all datasets

This script runs hyperparameter tuning across all available datasets
and provides options for different tuning strategies.

Usage examples:

1. Single dataset tuning:
   python run_massive_ray_tuning.py --single-dataset Allen_2022-04-13.yaml

2. All datasets with default settings:
   python run_massive_ray_tuning.py --all-datasets

3. Custom tuning with more trials:
   python run_massive_ray_tuning.py --all-datasets --num-trials 200 --max-epochs 30

4. Resume interrupted experiment:
   python run_massive_ray_tuning.py --all-datasets --resume

5. Test run with fewer trials:
   python run_massive_ray_tuning.py --all-datasets --num-trials 10 --max-epochs 5 --test-mode
"""

import os
import sys
import argparse
from pathlib import Path
import subprocess
import time
import yaml

# Configuration paths
DATASET_CONFIGS_DIR = "/mnt/ssd/YatesMarmoV1/conv_model_fits/data_configs/multi_dataset_temporal_basis"
MODEL_CONFIG_PATH = "/home/<USER>/repos/DataYatesV1/examples/configs/test_model_stimembed_3dconv_big_multi.yaml"
RAY_SCRIPT_PATH = "/home/<USER>/repos/DataYatesV1/examples/model_train_ray.py"

def get_available_datasets():
    """Get list of available dataset configurations."""
    dataset_dir = Path(DATASET_CONFIGS_DIR)
    if not dataset_dir.exists():
        raise FileNotFoundError(f"Dataset directory not found: {dataset_dir}")
    
    datasets = list(dataset_dir.glob("*.yaml"))
    # Filter out the base config
    datasets = [d for d in datasets if "base" not in d.name.lower()]
    
    return sorted(datasets)

def estimate_runtime(num_datasets, num_trials, max_epochs_per_trial, num_gpus=1):
    """Estimate total runtime for the experiment."""
    # Rough estimates based on typical training times
    minutes_per_epoch = 2  # Conservative estimate
    minutes_per_trial = max_epochs_per_trial * minutes_per_epoch * 0.5  # Early stopping factor
    total_minutes = (num_datasets * num_trials * minutes_per_trial) / num_gpus
    
    hours = total_minutes / 60
    days = hours / 24
    
    return {
        'minutes': total_minutes,
        'hours': hours,
        'days': days
    }

def run_single_dataset(dataset_name, num_trials, max_epochs_per_trial, test_mode=False):
    """Run hyperparameter tuning on a single dataset."""
    dataset_path = Path(DATASET_CONFIGS_DIR) / dataset_name
    if not dataset_path.exists():
        raise FileNotFoundError(f"Dataset config not found: {dataset_path}")
    
    cmd = [
        "python", RAY_SCRIPT_PATH,
        "--dataset-config", str(dataset_path),
        "--model-config", MODEL_CONFIG_PATH,
        "--num-trials", str(num_trials),
        "--max-epochs-per-trial", str(max_epochs_per_trial)
    ]
    
    if test_mode:
        cmd.extend(["--experiment-name", f"test_{dataset_name.replace('.yaml', '')}"])
    
    print(f"Running command: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("STDOUT:", result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error running {dataset_name}: {e}")
        print("STDERR:", e.stderr)
        return False

def run_all_datasets(num_trials, max_epochs_per_trial, resume=False, test_mode=False):
    """Run hyperparameter tuning on all datasets."""
    cmd = [
        "python", RAY_SCRIPT_PATH,
        "--batch-mode",
        "--dataset-configs-dir", DATASET_CONFIGS_DIR,
        "--model-config", MODEL_CONFIG_PATH,
        "--num-trials", str(num_trials),
        "--max-epochs-per-trial", str(max_epochs_per_trial)
    ]
    
    if resume:
        cmd.append("--resume")
    
    if test_mode:
        cmd.extend(["--experiment-name", "test_batch_run"])
    
    print(f"Running command: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True)
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error in batch processing: {e}")
        return False

def create_experiment_summary(datasets, num_trials, max_epochs_per_trial, estimated_runtime):
    """Create a summary of the planned experiment."""
    summary = {
        'experiment_type': 'massive_hyperparameter_tuning',
        'timestamp': time.strftime('%Y-%m-%d_%H-%M-%S'),
        'datasets': {
            'total_count': len(datasets),
            'dataset_names': [d.name for d in datasets]
        },
        'hyperparameters': {
            'num_trials_per_dataset': num_trials,
            'max_epochs_per_trial': max_epochs_per_trial,
            'total_trials': len(datasets) * num_trials
        },
        'estimated_runtime': estimated_runtime,
        'model_config': MODEL_CONFIG_PATH,
        'dataset_configs_dir': DATASET_CONFIGS_DIR
    }
    
    return summary

def main():
    parser = argparse.ArgumentParser(description='Massive hyperparameter tuning for all datasets')
    
    # Mode selection
    mode_group = parser.add_mutually_exclusive_group(required=True)
    mode_group.add_argument('--single-dataset', type=str,
                           help='Run on single dataset (provide filename)')
    mode_group.add_argument('--all-datasets', action='store_true',
                           help='Run on all available datasets')
    
    # Tuning parameters
    parser.add_argument('--num-trials', type=int, default=100,
                       help='Number of trials per dataset (default: 100)')
    parser.add_argument('--max-epochs', type=int, default=25,
                       help='Maximum epochs per trial (default: 25)')
    
    # Experiment management
    parser.add_argument('--resume', action='store_true',
                       help='Resume interrupted experiments')
    parser.add_argument('--test-mode', action='store_true',
                       help='Run in test mode with reduced trials/epochs')
    
    # Information
    parser.add_argument('--list-datasets', action='store_true',
                       help='List available datasets and exit')
    parser.add_argument('--estimate-time', action='store_true',
                       help='Estimate runtime and exit')
    
    args = parser.parse_args()
    
    # Handle information requests
    if args.list_datasets:
        datasets = get_available_datasets()
        print(f"Available datasets ({len(datasets)}):")
        for i, dataset in enumerate(datasets, 1):
            print(f"  {i:2d}. {dataset.name}")
        return
    
    # Adjust parameters for test mode
    if args.test_mode:
        args.num_trials = min(args.num_trials, 10)
        args.max_epochs = min(args.max_epochs, 5)
        print("🧪 TEST MODE: Reduced trials and epochs")
    
    # Get datasets
    if args.single_dataset:
        datasets = [Path(DATASET_CONFIGS_DIR) / args.single_dataset]
        if not datasets[0].exists():
            print(f"❌ Dataset not found: {datasets[0]}")
            return 1
    else:
        datasets = get_available_datasets()
    
    # Estimate runtime
    estimated_runtime = estimate_runtime(
        len(datasets), args.num_trials, args.max_epochs
    )
    
    if args.estimate_time:
        print(f"Runtime estimation for {len(datasets)} datasets:")
        print(f"  Trials per dataset: {args.num_trials}")
        print(f"  Max epochs per trial: {args.max_epochs}")
        print(f"  Total trials: {len(datasets) * args.num_trials}")
        print(f"  Estimated time: {estimated_runtime['days']:.1f} days ({estimated_runtime['hours']:.1f} hours)")
        return
    
    # Create experiment summary
    summary = create_experiment_summary(datasets, args.num_trials, args.max_epochs, estimated_runtime)
    
    print("🚀 MASSIVE HYPERPARAMETER TUNING")
    print("=" * 50)
    print(f"Datasets: {len(datasets)}")
    print(f"Trials per dataset: {args.num_trials}")
    print(f"Max epochs per trial: {args.max_epochs}")
    print(f"Total trials: {len(datasets) * args.num_trials}")
    print(f"Estimated runtime: {estimated_runtime['days']:.1f} days")
    print("=" * 50)
    
    # Confirm before starting
    if not args.test_mode:
        response = input("Continue? (y/N): ")
        if response.lower() != 'y':
            print("Aborted.")
            return 0
    
    # Save experiment summary
    summary_dir = Path("/mnt/ssd/YatesMarmoV1/conv_model_fits/ray_experiments")
    summary_dir.mkdir(parents=True, exist_ok=True)
    summary_path = summary_dir / f"experiment_summary_{summary['timestamp']}.yaml"
    
    with open(summary_path, 'w') as f:
        yaml.dump(summary, f, default_flow_style=False)
    
    print(f"📝 Experiment summary saved: {summary_path}")
    
    # Run the experiment
    start_time = time.time()
    
    if args.single_dataset:
        success = run_single_dataset(
            args.single_dataset, args.num_trials, args.max_epochs, args.test_mode
        )
    else:
        success = run_all_datasets(
            args.num_trials, args.max_epochs, args.resume, args.test_mode
        )
    
    end_time = time.time()
    actual_runtime = (end_time - start_time) / 3600  # hours
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 EXPERIMENT COMPLETED SUCCESSFULLY!")
    else:
        print("❌ EXPERIMENT FAILED!")
    
    print(f"Actual runtime: {actual_runtime:.1f} hours")
    print(f"Estimated runtime: {estimated_runtime['hours']:.1f} hours")
    print("=" * 50)
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
