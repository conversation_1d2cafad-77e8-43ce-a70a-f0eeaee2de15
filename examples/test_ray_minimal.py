#!/usr/bin/env python
"""
Minimal test for Ray hyperparameter tuning pipeline

This script runs a very quick test (3 trials, 3 epochs max) to verify:
1. <PERSON> works with your model pipeline
2. Model registry integration works
3. Experiment management works
4. Results are properly saved

Usage:
    python test_ray_minimal.py

This should complete in ~5-10 minutes and can be safely cleaned up afterward.
"""

import os
import sys
import time
import shutil
from pathlib import Path
import subprocess

# Configuration for minimal test
TEST_CONFIG = {
    'num_trials': 3,
    'max_epochs_per_trial': 3,
    'experiment_name': 'minimal_test',
    'dataset_config': '/mnt/ssd/YatesMarmoV1/conv_model_fits/data_configs/multi_dataset_temporal_basis/Allen_2022-04-13.yaml',
    'model_config': '/home/<USER>/repos/DataYatesV1/examples/configs/test_model_stimembed_dense.yaml'
}

def run_minimal_test():
    """Run the minimal Ray tuning test."""
    print("🧪 Running Minimal Ray Hyperparameter Tuning Test")
    print("=" * 60)
    print(f"Dataset: {Path(TEST_CONFIG['dataset_config']).name}")
    print(f"Trials: {TEST_CONFIG['num_trials']}")
    print(f"Max epochs per trial: {TEST_CONFIG['max_epochs_per_trial']}")
    print(f"Expected runtime: ~5-10 minutes")
    print("=" * 60)
    
    # Build command
    cmd = [
        "python", "model_train_ray.py",
        "--dataset-config", TEST_CONFIG['dataset_config'],
        "--model-config", TEST_CONFIG['model_config'],
        "--num-trials", str(TEST_CONFIG['num_trials']),
        "--max-epochs-per-trial", str(TEST_CONFIG['max_epochs_per_trial']),
        "--experiment-name", TEST_CONFIG['experiment_name']
    ]
    
    print(f"Running command: {' '.join(cmd)}")
    print("\nStarting test...")
    
    start_time = time.time()
    
    try:
        # Run the test
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        
        end_time = time.time()
        runtime_minutes = (end_time - start_time) / 60
        
        print(f"\n✅ Test completed successfully in {runtime_minutes:.1f} minutes!")
        
        # Show some output
        print("\n--- Last 20 lines of output ---")
        output_lines = result.stdout.split('\n')
        for line in output_lines[-20:]:
            if line.strip():
                print(line)
        
        return True, runtime_minutes
        
    except subprocess.CalledProcessError as e:
        end_time = time.time()
        runtime_minutes = (end_time - start_time) / 60
        
        print(f"\n❌ Test failed after {runtime_minutes:.1f} minutes")
        print("\n--- Error output ---")
        print(e.stderr)
        print("\n--- Standard output ---")
        print(e.stdout)
        
        return False, runtime_minutes

def check_results():
    """Check that the test produced expected results."""
    print("\n🔍 Checking test results...")
    
    # Check experiment directory
    experiment_dir = Path(f"/mnt/ssd/YatesMarmoV1/conv_model_fits/ray_experiments")
    experiment_dirs = list(experiment_dir.glob(f"*{TEST_CONFIG['experiment_name']}*"))
    
    if experiment_dirs:
        exp_dir = experiment_dirs[0]
        print(f"✅ Experiment directory created: {exp_dir.name}")
        
        # Check for summary file
        summary_files = list(exp_dir.glob("experiment_summary.yaml"))
        if summary_files:
            print("✅ Experiment summary created")
        else:
            print("⚠️  Experiment summary not found")
        
        # Check for trial directories
        trial_dirs = list(exp_dir.glob("trial_*"))
        print(f"✅ Found {len(trial_dirs)} trial directories")
        
    else:
        print("❌ No experiment directory found")
        return False
    
    # Check model registry
    registry_dir = Path("/mnt/ssd/YatesMarmoV1/conv_model_fits/model_registry_ray")
    if registry_dir.exists():
        registry_files = list(registry_dir.glob("*.json"))
        if registry_files:
            print("✅ Model registry files found")
        else:
            print("⚠️  No model registry files found")
    else:
        print("⚠️  Model registry directory not found")
    
    # Check for final model
    runs_dir = Path("/mnt/ssd/YatesMarmoV1/conv_model_fits/runs")
    ray_model_dirs = list(runs_dir.glob("ray_tuned_*"))
    if ray_model_dirs:
        print(f"✅ Found {len(ray_model_dirs)} Ray-tuned model(s)")
    else:
        print("⚠️  No Ray-tuned models found in runs directory")
    
    return True

def cleanup_test_files():
    """Clean up test files to prepare for real experiments."""
    print("\n🧹 Cleaning up test files...")
    
    cleanup_paths = [
        f"/mnt/ssd/YatesMarmoV1/conv_model_fits/ray_experiments/*{TEST_CONFIG['experiment_name']}*",
        "/mnt/ssd/YatesMarmoV1/conv_model_fits/runs/ray_tuned_*minimal_test*",
        "/tmp/ray_trial_*"
    ]
    
    cleaned_count = 0
    
    for pattern in cleanup_paths:
        from glob import glob
        matching_paths = glob(pattern)
        
        for path in matching_paths:
            path_obj = Path(path)
            try:
                if path_obj.is_dir():
                    shutil.rmtree(path_obj)
                    print(f"  Removed directory: {path_obj.name}")
                else:
                    path_obj.unlink()
                    print(f"  Removed file: {path_obj.name}")
                cleaned_count += 1
            except Exception as e:
                print(f"  ⚠️  Could not remove {path_obj}: {e}")
    
    # Clean up model registry entries (more careful approach)
    registry_dir = Path("/mnt/ssd/YatesMarmoV1/conv_model_fits/model_registry_ray")
    if registry_dir.exists():
        try:
            from DataYatesV1.models.model_manager import ModelRegistry
            registry = ModelRegistry(registry_dir)
            
            # This would require implementing a cleanup method in ModelRegistry
            # For now, just note that manual cleanup might be needed
            print("  ⚠️  Model registry cleanup may need manual attention")
            
        except Exception as e:
            print(f"  ⚠️  Could not clean model registry: {e}")
    
    print(f"✅ Cleaned up {cleaned_count} test files/directories")
    
    return cleaned_count > 0

def main():
    """Run the complete minimal test workflow."""
    print("🚀 Ray Hyperparameter Tuning - Minimal Test")
    print("This will run a quick test to verify everything works before the massive experiment.")
    print()
    
    # Confirm before running
    response = input("Run minimal test? This will take ~5-10 minutes (y/N): ")
    if response.lower() != 'y':
        print("Test cancelled.")
        return 0
    
    # Run the test
    success, runtime = run_minimal_test()
    
    if success:
        # Check results
        check_results()
        
        print("\n" + "=" * 60)
        print("✅ MINIMAL TEST COMPLETED SUCCESSFULLY!")
        print("=" * 60)
        print(f"Runtime: {runtime:.1f} minutes")
        print("The Ray hyperparameter tuning pipeline is working correctly.")
        print()
        print("Next steps:")
        print("1. Review the results in the experiment directory")
        print("2. Check the model registry")
        print("3. Clean up test files (optional)")
        print("4. Run the massive experiment when ready")
        
        # Offer cleanup
        print()
        cleanup_response = input("Clean up test files now? (y/N): ")
        if cleanup_response.lower() == 'y':
            cleanup_test_files()
            print("✅ Test files cleaned up. Ready for massive experiment!")
        else:
            print("Test files preserved. You can clean them up later with:")
            print("python test_ray_minimal.py --cleanup-only")
        
        return 0
        
    else:
        print("\n" + "=" * 60)
        print("❌ MINIMAL TEST FAILED!")
        print("=" * 60)
        print(f"Runtime: {runtime:.1f} minutes")
        print("Please check the error messages above and fix issues before running massive experiment.")
        
        return 1

def cleanup_only():
    """Just run cleanup without testing."""
    print("🧹 Cleaning up test files only...")
    cleanup_test_files()
    print("✅ Cleanup complete!")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--cleanup-only":
        cleanup_only()
    else:
        sys.exit(main())
