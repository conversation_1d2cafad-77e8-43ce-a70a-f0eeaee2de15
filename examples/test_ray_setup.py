#!/usr/bin/env python
"""
Test script to verify Ray hyperparameter tuning setup

This script performs basic checks to ensure the Ray tuning pipeline
will work correctly before running expensive experiments.

Usage:
    python test_ray_setup.py
"""

import os
import sys
import torch
import yaml
from pathlib import Path
import tempfile

def test_imports():
    """Test that all required packages are available."""
    print("Testing imports...")
    
    try:
        import ray
        print(f"✅ Ray: {ray.__version__}")
    except ImportError as e:
        print(f"❌ Ray import failed: {e}")
        return False
    
    try:
        from ray import tune
        print("✅ Ray Tune")
    except ImportError as e:
        print(f"❌ Ray Tune import failed: {e}")
        return False
    
    try:
        import optuna
        print(f"✅ Optuna: {optuna.__version__}")
    except ImportError as e:
        print(f"❌ Optuna import failed: {e}")
        return False
    
    try:
        import lightning as pl
        print(f"✅ PyTorch Lightning: {pl.__version__}")
    except ImportError as e:
        print(f"❌ PyTorch Lightning import failed: {e}")
        return False
    
    try:
        from DataYatesV1.models import build_model
        print("✅ DataYatesV1 models")
    except ImportError as e:
        print(f"❌ DataYatesV1 import failed: {e}")
        return False
    
    return True

def test_ray_initialization():
    """Test Ray initialization and shutdown."""
    print("\nTesting Ray initialization...")
    
    try:
        import ray
        
        # Initialize Ray with minimal resources
        ray.init(num_cpus=2, num_gpus=0, include_dashboard=False, ignore_reinit_error=True)
        print("✅ Ray initialized successfully")
        
        # Test basic Ray functionality
        @ray.remote
        def test_function(x):
            return x * 2
        
        result = ray.get(test_function.remote(5))
        if result == 10:
            print("✅ Ray remote function works")
        else:
            print(f"❌ Ray remote function failed: expected 10, got {result}")
            return False
        
        # Shutdown Ray
        ray.shutdown()
        print("✅ Ray shutdown successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Ray test failed: {e}")
        return False

def test_config_files():
    """Test that required configuration files exist and are valid."""
    print("\nTesting configuration files...")
    
    # Test dataset configs directory
    dataset_dir = Path("/mnt/ssd/YatesMarmoV1/conv_model_fits/data_configs/multi_dataset_temporal_basis")
    if dataset_dir.exists():
        dataset_files = list(dataset_dir.glob("*.yaml"))
        print(f"✅ Found {len(dataset_files)} dataset configs")
        
        # Test loading one dataset config
        if dataset_files:
            try:
                with open(dataset_files[0], 'r') as f:
                    config = yaml.safe_load(f)
                required_keys = ['session', 'cids', 'types']
                if all(key in config for key in required_keys):
                    print("✅ Dataset config format valid")
                else:
                    print(f"❌ Dataset config missing required keys: {required_keys}")
                    return False
            except Exception as e:
                print(f"❌ Failed to load dataset config: {e}")
                return False
    else:
        print(f"❌ Dataset configs directory not found: {dataset_dir}")
        return False
    
    # Test model config
    model_config_path = Path("/home/<USER>/repos/DataYatesV1/examples/configs/test_model_stimembed_3dconv_big_multi.yaml")
    if model_config_path.exists():
        try:
            with open(model_config_path, 'r') as f:
                config = yaml.safe_load(f)
            print("✅ Model config exists and loads")
        except Exception as e:
            print(f"❌ Failed to load model config: {e}")
            return False
    else:
        print(f"❌ Model config not found: {model_config_path}")
        return False
    
    return True

def test_gpu_availability():
    """Test GPU availability and CUDA setup."""
    print("\nTesting GPU setup...")
    
    if torch.cuda.is_available():
        num_gpus = torch.cuda.device_count()
        print(f"✅ CUDA available with {num_gpus} GPU(s)")
        
        for i in range(num_gpus):
            gpu_name = torch.cuda.get_device_name(i)
            memory = torch.cuda.get_device_properties(i).total_memory / 1e9
            print(f"  GPU {i}: {gpu_name} ({memory:.1f} GB)")
        
        # Test basic GPU operation
        try:
            x = torch.randn(100, 100).cuda()
            y = torch.mm(x, x.t())
            print("✅ Basic GPU operations work")
        except Exception as e:
            print(f"❌ GPU operation failed: {e}")
            return False
    else:
        print("⚠️  CUDA not available - will use CPU only")
    
    return True

def test_directory_permissions():
    """Test that we can write to required directories."""
    print("\nTesting directory permissions...")
    
    directories = [
        "/mnt/ssd/YatesMarmoV1/conv_model_fits/ray_experiments",
        "/mnt/ssd/YatesMarmoV1/conv_model_fits/model_registry_ray",
        "/mnt/ssd/YatesMarmoV1/conv_model_fits/runs"
    ]
    
    for directory in directories:
        dir_path = Path(directory)
        try:
            dir_path.mkdir(parents=True, exist_ok=True)
            
            # Test write permission
            test_file = dir_path / "test_write.tmp"
            test_file.write_text("test")
            test_file.unlink()
            
            print(f"✅ {directory} - writable")
        except Exception as e:
            print(f"❌ {directory} - not writable: {e}")
            return False
    
    return True

def test_data_loading():
    """Test basic data loading functionality."""
    print("\nTesting data loading...")
    
    try:
        from DataYatesV1.utils.data import prepare_data
        
        # Find a dataset config to test with
        dataset_dir = Path("/mnt/ssd/YatesMarmoV1/conv_model_fits/data_configs/multi_dataset_temporal_basis")
        dataset_files = list(dataset_dir.glob("*.yaml"))
        
        if not dataset_files:
            print("❌ No dataset configs found for testing")
            return False
        
        # Load a small dataset config for testing
        test_config_path = dataset_files[0]
        with open(test_config_path, 'r') as f:
            dataset_config = yaml.safe_load(f)
        
        # Limit to fewer units for testing
        if len(dataset_config.get('cids', [])) > 5:
            dataset_config['cids'] = dataset_config['cids'][:5]
        
        print(f"Testing data loading with {test_config_path.name}...")
        
        # This might take a while, so we'll just test the import
        print("✅ Data loading functions available")
        
        return True
        
    except Exception as e:
        print(f"❌ Data loading test failed: {e}")
        return False

def test_model_building():
    """Test basic model building functionality."""
    print("\nTesting model building...")
    
    try:
        from DataYatesV1.models import build_model
        from DataYatesV1.models.config_loader import load_config
        
        # Load model config
        model_config_path = Path("/home/<USER>/repos/DataYatesV1/examples/configs/test_model_stimembed_3dconv_big_multi.yaml")
        if not model_config_path.exists():
            print("❌ Model config not found")
            return False
        
        config = load_config(model_config_path)
        config['readout']['params']['n_units'] = 5  # Small number for testing
        
        print("✅ Model config loaded")
        print("✅ Model building functions available")
        
        return True
        
    except Exception as e:
        print(f"❌ Model building test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🧪 Testing Ray Hyperparameter Tuning Setup")
    print("=" * 50)
    
    tests = [
        ("Package Imports", test_imports),
        ("Ray Initialization", test_ray_initialization),
        ("Configuration Files", test_config_files),
        ("GPU Availability", test_gpu_availability),
        ("Directory Permissions", test_directory_permissions),
        ("Data Loading", test_data_loading),
        ("Model Building", test_model_building),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("TEST SUMMARY")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{len(results)}")
    
    if passed == len(results):
        print("\n🎉 All tests passed! Ready for Ray hyperparameter tuning.")
        return 0
    else:
        print(f"\n⚠️  {len(results) - passed} test(s) failed. Please fix issues before running experiments.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
