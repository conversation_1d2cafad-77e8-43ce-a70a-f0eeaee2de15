"""
Ray<PERSON>Tune driver for LR/weight‑decay sweeps on vision models
---------------------------------------------------------
*   Handles **large datasets** (6e5 × 51×51) by **loading inside each worker**.
*   Sweeps only **learning‑rate** & **weight‑decay** for speed / stability.
*   Supports multiple model‑config YAMLs (e.g. ResNet / DenseNet ± modulator).
*   Designed for ≈30 datasets over a week on a multi‑GPU server.

Run examples
~~~~~~~~~~~~
Single dataset + one model config
>>> python ray_tune_image_model.py \
        --dataset-config   /data/ds.yaml \
        --model-configs    resnet.yaml densenet.yaml \
        --gpus-per-trial   1 --cpus-per-trial 8 --trials 25

Batch mode over directory of datasets (looped *outside* Ray)
>>> python ray_tune_image_model.py \
        --dataset-dir /data/all_sessions \
        --model-configs resnet.yaml densenet.yaml mod_resnet.yaml mod_densenet.yaml \
        --batch
"""

from __future__ import annotations
import os, time, argparse, yaml, copy, warnings, contextlib
from pathlib import Path
from typing import Dict, Any, List

import numpy as np
import torch
from torch.utils.data import DataLoader

# Lightning
import lightning as pl
from lightning.pytorch.callbacks import EarlyStopping, ModelCheckpoint

# Ray‑Tune
import ray
from ray import tune
from ray.tune.schedulers import ASHAScheduler
from ray.tune.search.optuna import OptunaSearch
import optuna

# Project‑local
from DataYatesV1.models import build_model, initialize_model_components
from DataYatesV1.models.config_loader import load_config
from DataYatesV1.models.lightning import PLCoreVisionModel
from DataYatesV1.utils.data import prepare_data
from DataYatesV1.models.utils.general import ValidateOnTrainStart

# --------------------------------------------------------------------------------------
#  Utility helpers
# --------------------------------------------------------------------------------------

def make_dataloaders(train_ds, val_ds, batch_size: int, workers: int):
    """Create DataLoaders with deterministic worker init."""
    g = torch.Generator(); g.manual_seed(42)

    def _init(_worker_id):
        s = torch.initial_seed() % 2**32; np.random.seed(s)

    loader_kwargs = dict(batch_size=batch_size, pin_memory=True,
                         num_workers=workers, worker_init_fn=_init,
                         generator=g)
    return (DataLoader(train_ds, shuffle=True , **loader_kwargs),
            DataLoader(val_ds  , shuffle=False, **loader_kwargs))


def inverse_softplus(x: torch.Tensor, beta: float = 1.):
    return torch.log(torch.exp(beta * x) - 1.) / beta

# --------------------------------------------------------------------------------------
#  Ray trainable — **loads dataset inside the worker** so nothing huge is pickled.
# --------------------------------------------------------------------------------------

def train_one_model(cfg: Dict[str, Any], *,
                    dataset_cfg_path: Path,
                    model_cfg_path  : Path,
                    max_epochs: int,
                    gpus_per_trial: int,
                    cpus_per_trial: int,
                    enable_wandb: bool = True):
    """Ray Tune trainable function (executed inside each worker)."""

    # 1.  Load dataset **locally** to avoid pickling giant objects
    train_ds, val_ds, dset_info = prepare_data(yaml.safe_load(Path(dataset_cfg_path).read_text()))

    # 2.  Prepare model config (deep‑copy so we don't mutate the original)
    model_cfg = load_config(model_cfg_path)
    lr, wd = cfg["lr"], cfg["weight_decay"]

    # Ensure readout matches dataset
    model_cfg.setdefault("readout", {}).setdefault("params", {})["n_units"] = len(dset_info["cids"])

    # Extract names for logging
    dataset_name = dataset_cfg_path.stem  # e.g., "Allen_2022-04-13"
    model_name = model_cfg_path.stem      # e.g., "resnet_modulator"

    # 3.  Lightning module
    pl_model = PLCoreVisionModel(
        model_class=build_model,
        model_config=model_cfg,
        optimizer="AdamW",
        optim_kwargs=dict(lr=lr, weight_decay=wd),
        dataset_info=dset_info,
    )

    # Bias init - handle dictionary format from your DataLoader
    train_loader_preview = DataLoader(train_ds, batch_size=1024, shuffle=False)

    # Debug: Check the actual format of the first batch
    first_batch = next(iter(train_loader_preview))
    print(f"Batch type: {type(first_batch)}")
    if isinstance(first_batch, dict):
        print(f"Batch keys: {list(first_batch.keys())}")
        if 'robs' in first_batch:
            print(f"robs shape: {first_batch['robs'].shape}")

    # Calculate firing rates based on actual data format
    try:
        if isinstance(first_batch, dict) and 'robs' in first_batch:
            # Dictionary format with 'robs' key
            fr = sum(batch['robs'].sum(0) for batch in train_loader_preview)
            n  = sum(len(batch['robs']) for batch in train_loader_preview)
        elif isinstance(first_batch, (tuple, list)) and len(first_batch) >= 2:
            # Tuple format (x, y)
            fr = sum(batch[1].sum(0) for batch in train_loader_preview)
            n  = sum(len(batch[1]) for batch in train_loader_preview)
        else:
            # Fallback: use zeros for bias initialization
            print("⚠️  Unknown batch format, using zero bias initialization")
            fr = torch.zeros(len(dset_info["cids"]))
            n = 1

        baseline_rates = fr / n
        initialize_model_components(pl_model.model, init_bias=inverse_softplus(baseline_rates))

    except Exception as e:
        print(f"⚠️  Bias initialization failed: {e}")
        print("Using zero bias initialization as fallback")
        zero_bias = torch.zeros(len(dset_info["cids"]))
        initialize_model_components(pl_model.model, init_bias=zero_bias)

    # 4.  Setup logging
    if enable_wandb:
        from lightning.pytorch.loggers import WandbLogger

        # Create unique run name for this trial
        trial_id = tune.get_trial_id() if hasattr(tune, 'get_trial_id') else "unknown"
        run_name = f"{dataset_name}_{model_name}_lr{lr:.1e}_wd{wd:.1e}_{trial_id}"

        # Setup wandb logger with grouping and tags
        logger = WandbLogger(
            project='Digital Twin Ray Tuning',
            name=run_name,
            group=dataset_name,  # Group by dataset for easy filtering
            tags=[
                model_name,      # Tag with model architecture
                'ray_tune',      # Tag as Ray Tune experiment
                'hyperparameter_search'
            ],
            save_code=True,
            entity='yateslab',
            # Log hyperparameters from Ray Tune
            config={
                'lr': lr,
                'weight_decay': wd,
                'batch_size': cfg['batch_size'],
                'dataset': dataset_name,
                'model': model_name,
                'max_epochs': max_epochs,
                'trial_id': trial_id
            }
        )
    else:
        logger = None

    # 5.  Callbacks
    chkpt_cb = ModelCheckpoint(monitor="val_loss", mode="min", save_last=False, save_top_k=1)
    es_cb    = EarlyStopping(monitor="val_loss", mode="min", patience=5)

    trainer = pl.Trainer(
        accelerator="gpu" if gpus_per_trial else "cpu",
        devices=gpus_per_trial or 1,
        max_epochs=max_epochs,
        callbacks=[chkpt_cb, es_cb, ValidateOnTrainStart()],
        logger=logger,
        enable_progress_bar=False,
        num_sanity_val_steps=0,
    )

    train_loader, val_loader = make_dataloaders(train_ds, val_ds,
                                                batch_size=cfg["batch_size"],
                                                workers=min(4, cpus_per_trial//2))

    trainer.fit(pl_model, train_loader, val_loader)

    final = trainer.callback_metrics.get("val_loss", torch.tensor(float("inf")))
    tune.report({"val_loss": final.item(), "epoch": trainer.current_epoch})

    # Free GPU mem before Ray reuses the worker
    torch.cuda.empty_cache()

# --------------------------------------------------------------------------------------
#  Search‑/scheduler setup
# --------------------------------------------------------------------------------------

def build_search():
    return {
        "lr"          : tune.loguniform(1e-5, 3e-3),
        "weight_decay": tune.loguniform(1e-7, 1e-3),
        "batch_size"  : tune.choice([128, 256, 512]),
    }

# --------------------------------------------------------------------------------------
#  Orchestrator for one (dataset, model) pair
# --------------------------------------------------------------------------------------

def sweep(dataset_cfg: Path, model_cfg: Path, *, args):
    ds_name   = dataset_cfg.stem
    model_tag = model_cfg.stem
    exp_name  = f"ray_{ds_name}_{model_tag}_{int(time.time())}"

    print(f"▶️  {exp_name}: {args.trials} trials @ {args.max_epochs} epochs each")

    algo      = OptunaSearch(metric="val_loss", mode="min",
                              sampler=optuna.samplers.TPESampler(seed=42))

    grace     = max(1, min(3, args.max_epochs - 1))
    scheduler = ASHAScheduler(max_t=args.max_epochs, grace_period=grace,
                              reduction_factor=3, metric="val_loss", mode="min")

    trainable = tune.with_parameters(
        train_one_model,
        dataset_cfg_path=dataset_cfg,
        model_cfg_path  =model_cfg,
        max_epochs=args.max_epochs,
        gpus_per_trial=args.gpus_per_trial,
        cpus_per_trial=args.cpus_per_trial,
        enable_wandb=True,  # Enable wandb for massive experiments
    )

    analysis = tune.run(
        trainable,
        name=exp_name,
        config=build_search(),
        num_samples=args.trials,
        scheduler=scheduler,
        search_alg=algo,
        resources_per_trial={"cpu": args.cpus_per_trial, "gpu": args.gpus_per_trial},
        max_concurrent_trials=args.max_concurrent,
        log_to_file=True,
    )

    best = analysis.get_best_result("val_loss", "min").metrics["val_loss"]
    print(f"✅ {exp_name} best val_loss: {best:.4f}")

# --------------------------------------------------------------------------------------
#  CLI / entry‑point
# --------------------------------------------------------------------------------------

def parse() -> argparse.Namespace:
    p = argparse.ArgumentParser()
    p.add_argument("--dataset-config", type=Path, help="Single dataset YAML")
    p.add_argument("--dataset-dir",   type=Path, help="Directory of dataset YAMLs (batch mode)")
    p.add_argument("--model-configs", type=Path, nargs="+", required=True,
                   help="One or more model config YAMLs")

    p.add_argument("--trials",     type=int, default=25)
    p.add_argument("--max-epochs", type=int, default=20)

    p.add_argument("--gpus-per-trial", type=float, default=1)
    p.add_argument("--cpus-per-trial", type=int,   default=8)
    p.add_argument("--max-concurrent", type=int,   default=4)

    p.add_argument("--batch", action="store_true", help="Loop over all datasets in directory")
    return p.parse_args()

# --------------------------------------------------------------------------------------
if __name__ == "__main__":
    args = parse()

    ray.init(num_cpus=os.cpu_count(), num_gpus=torch.cuda.device_count(), include_dashboard=False)

    dataset_paths: List[Path]
    if args.batch:
        dataset_paths = sorted(Path(args.dataset_dir).glob("*.yaml"))
    else:
        dataset_paths = [args.dataset_config]

    for ds in dataset_paths:
        for model_cfg in args.model_configs:
            sweep(ds, model_cfg, args=args)

    print("🎯 All sweeps finished")
