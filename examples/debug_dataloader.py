#!/usr/bin/env python
"""
Standalone DataLoader debugging script for multidataset training.

This script helps debug DataLoader infinite loops by testing data loading
in isolation before running the full training pipeline.

Usage:
    python examples/debug_dataloader.py --train-config path/to/config.yaml
    python examples/debug_dataloader.py --train-config path/to/config.yaml --num-workers 0
    python examples/debug_dataloader.py --train-config path/to/config.yaml --batch-size 32
"""

import torch
import argparse
import sys
import signal
import time
from pathlib import Path
from torch.utils.data import DataLoader

# Add the project root to the path
sys.path.append(str(Path(__file__).parent.parent))

from DataYatesV1.models.config_loader import load_multidataset_config
from DataYatesV1.utils.data import prepare_multidataset_data

def timeout_handler(signum, frame):
    raise TimeoutError("DataLoader operation timed out")

def test_dataset_loading(dataset_config, use_fp16=False):
    """Test loading a single dataset."""
    print(f"\n🔍 Testing dataset loading...")
    
    try:
        # Set timeout
        signal.signal(signal.SIGALRM, timeout_handler)
        signal.alarm(60)  # 60 second timeout
        
        start_time = time.time()
        train_dset, val_dset, updated_config = prepare_multidataset_data([dataset_config], use_fp16=use_fp16)
        load_time = time.time() - start_time
        
        signal.alarm(0)  # Cancel timeout
        
        print(f"✅ Dataset loading completed in {load_time:.2f}s")
        print(f"   Train datasets: {len(train_dset)}")
        print(f"   Val datasets: {len(val_dset)}")
        
        return train_dset, val_dset, updated_config
        
    except TimeoutError:
        print("❌ Dataset loading timed out!")
        return None, None, None
    except Exception as e:
        print(f"❌ Dataset loading failed: {e}")
        return None, None, None

def test_dataloader(dataloader, name="DataLoader", max_batches=3, timeout_per_batch=30):
    """Test a DataLoader for infinite loops."""
    print(f"\n🔍 Testing {name}...")
    
    try:
        for i, batch in enumerate(dataloader):
            if i >= max_batches:
                break
                
            # Set timeout for this batch
            signal.signal(signal.SIGALRM, timeout_handler)
            signal.alarm(timeout_per_batch)
            
            try:
                start_time = time.time()
                
                # Test basic batch properties
                if isinstance(batch, dict):
                    print(f"  Batch {i}: {len(batch)} keys")
                    for key, value in batch.items():
                        if hasattr(value, 'shape'):
                            print(f"    {key}: {value.shape} ({value.dtype})")
                        else:
                            print(f"    {key}: {type(value)}")
                else:
                    print(f"  Batch {i}: {type(batch)}")
                
                load_time = time.time() - start_time
                print(f"  ✅ Batch {i} loaded in {load_time:.3f}s")
                
            except TimeoutError:
                print(f"  ❌ Batch {i} timed out after {timeout_per_batch}s")
                return False
            finally:
                signal.alarm(0)
        
        print(f"✅ {name} test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ {name} test failed: {e}")
        return False

def debug_multidataset_loading(train_config_path, batch_size=64, num_workers=0, use_fp16=False):
    """Debug multidataset loading step by step."""
    print("=" * 60)
    print("MULTIDATASET DATALOADER DEBUGGING")
    print("=" * 60)
    
    # Load configuration
    print(f"Loading config from: {train_config_path}")
    try:
        model_config, dataset_configs = load_multidataset_config(train_config_path)
        print(f"✅ Config loaded: {len(dataset_configs)} datasets")
    except Exception as e:
        print(f"❌ Config loading failed: {e}")
        return False
    
    # Test data preparation
    print(f"\n🔍 Testing data preparation...")
    try:
        signal.signal(signal.SIGALRM, timeout_handler)
        signal.alarm(120)  # 2 minute timeout for data prep
        
        start_time = time.time()
        train_datasets_dict, val_datasets_dict, updated_configs = prepare_multidataset_data(
            dataset_configs, use_fp16=use_fp16
        )
        prep_time = time.time() - start_time
        
        signal.alarm(0)
        print(f"✅ Data preparation completed in {prep_time:.2f}s")
        
    except TimeoutError:
        print("❌ Data preparation timed out!")
        return False
    except Exception as e:
        print(f"❌ Data preparation failed: {e}")
        return False
    
    # Test individual DataLoaders
    print(f"\n🔍 Testing individual DataLoaders (batch_size={batch_size}, num_workers={num_workers})...")
    
    for dataset_name in train_datasets_dict.keys():
        print(f"\n--- Testing {dataset_name} ---")
        
        # Test train DataLoader
        train_loader = DataLoader(
            train_datasets_dict[dataset_name],
            batch_size=batch_size,
            shuffle=True,
            num_workers=num_workers,
            pin_memory=True,
            timeout=30 if num_workers > 0 else 0
        )
        
        if not test_dataloader(train_loader, f"Train {dataset_name}", max_batches=2):
            print(f"❌ Train DataLoader for {dataset_name} failed!")
            return False
        
        # Test val DataLoader
        val_loader = DataLoader(
            val_datasets_dict[dataset_name],
            batch_size=batch_size,
            shuffle=False,
            num_workers=num_workers,
            pin_memory=True,
            timeout=30 if num_workers > 0 else 0
        )
        
        if not test_dataloader(val_loader, f"Val {dataset_name}", max_batches=2):
            print(f"❌ Val DataLoader for {dataset_name} failed!")
            return False
    
    # Test combined DataLoaders
    print(f"\n🔍 Testing combined DataLoaders...")
    try:
        from lightning.pytorch.utilities import CombinedLoader
        
        # Create individual loaders
        train_loaders = {}
        val_loaders = {}
        
        for dataset_name in train_datasets_dict.keys():
            train_loaders[dataset_name] = DataLoader(
                train_datasets_dict[dataset_name],
                batch_size=batch_size,
                shuffle=True,
                num_workers=num_workers,
                pin_memory=True,
                timeout=30 if num_workers > 0 else 0
            )
            
            val_loaders[dataset_name] = DataLoader(
                val_datasets_dict[dataset_name],
                batch_size=batch_size,
                shuffle=False,
                num_workers=num_workers,
                pin_memory=True,
                timeout=30 if num_workers > 0 else 0
            )
        
        # Create combined loaders
        combined_train_loader = CombinedLoader(train_loaders, mode="min_size")
        combined_val_loader = CombinedLoader(val_loaders, mode="min_size")
        
        # Test combined loaders
        if not test_dataloader(combined_train_loader, "Combined Train", max_batches=2):
            print("❌ Combined train DataLoader failed!")
            return False
            
        if not test_dataloader(combined_val_loader, "Combined Val", max_batches=2):
            print("❌ Combined val DataLoader failed!")
            return False
            
    except Exception as e:
        print(f"❌ Combined DataLoader creation failed: {e}")
        return False
    
    print("\n🎉 All DataLoader tests passed!")
    print("\nRecommendations:")
    print(f"- Use num_workers={num_workers} for training")
    print(f"- Use batch_size={batch_size} or larger")
    if use_fp16:
        print("- FP16 casting works correctly")
    
    return True

def main():
    parser = argparse.ArgumentParser(description='Debug DataLoader for multidataset training')
    parser.add_argument('--train-config', type=str, required=True,
                        help='Path to multidataset training configuration file')
    parser.add_argument('--batch-size', type=int, default=64,
                        help='Batch size for testing (default: 64)')
    parser.add_argument('--num-workers', type=int, default=0,
                        help='Number of DataLoader workers (default: 0)')
    parser.add_argument('--fp16', action='store_true',
                        help='Test with FP16 casting')
    
    args = parser.parse_args()
    
    # Validate config file exists
    config_path = Path(args.train_config)
    if not config_path.exists():
        print(f"Error: Configuration file not found: {config_path}")
        sys.exit(1)
    
    # Run debugging
    success = debug_multidataset_loading(
        str(config_path), 
        batch_size=args.batch_size,
        num_workers=args.num_workers,
        use_fp16=args.fp16
    )
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
