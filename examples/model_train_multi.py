#!/usr/bin/env python
"""
Example training a model from config

This script demonstrates:
1. Builds a model from a config
2. Prepares data from config
3. Runs training with specified arguments
4. Registers the model in a registry for easy loading

Usage:
    python model_train.py --config configs/v1_cones_dense_gaussian_standard_mish.yaml --test

Optional arguments

"""
#%%
import torch
from torch.utils.data import DataLoader
import argparse
import sys
import time
import yaml

import lightning as pl
from lightning.pytorch.loggers import WandbLogger
from lightning.pytorch.profilers import PyTorchProfiler

from pathlib import Path
import numpy as np
from DataYatesV1.models import build_model, initialize_model_components, get_name_from_config
from DataYatesV1.models.config_loader import load_multidataset_config
from DataYatesV1.models.lightning import MultiDatasetPLCore
from DataYatesV1.models.model_manager import ModelRegistry
from DataYatesV1.utils.data import prepare_multidataset_data
from DataYatesV1.utils.torch import get_free_device
from DataYatesV1.models.utils.general import ValidateOnTrainStart
import sys
import codename

# Set random seed for reproducibility
torch.manual_seed(42)
np.random.seed(42)

registry_dir = Path("/mnt/ssd/YatesMarmoV1/conv_model_fits/model_registry_multi")
registry = ModelRegistry(registry_dir)

#%%
def debug_dataloader(dataloader, name="DataLoader", max_batches=3, timeout_seconds=30):
    """
    Debug a DataLoader to check for infinite loops or hanging issues.

    Args:
        dataloader: The DataLoader to test
        name: Name for logging
        max_batches: Maximum number of batches to test
        timeout_seconds: Timeout for each batch

    Returns:
        bool: True if DataLoader works, False if it hangs
    """
    import signal
    import time

    def timeout_handler(signum, frame):
        raise TimeoutError(f"DataLoader batch loading timed out after {timeout_seconds} seconds")

    print(f"\n🔍 Testing {name} (max {max_batches} batches, {timeout_seconds}s timeout per batch)...")

    try:
        for i, batch in enumerate(dataloader):
            if i >= max_batches:
                break

            # Set timeout for this batch
            signal.signal(signal.SIGALRM, timeout_handler)
            signal.alarm(timeout_seconds)

            try:
                start_time = time.time()

                # Test basic batch properties
                if isinstance(batch, dict):
                    print(f"  Batch {i}: {len(batch)} keys")
                    for key, value in batch.items():
                        if hasattr(value, 'shape'):
                            print(f"    {key}: {value.shape} ({value.dtype})")
                        else:
                            print(f"    {key}: {type(value)}")
                else:
                    print(f"  Batch {i}: {type(batch)}")

                load_time = time.time() - start_time
                print(f"  ✅ Batch {i} loaded successfully in {load_time:.3f}s")

            except TimeoutError as e:
                print(f"  ❌ {e}")
                return False
            finally:
                signal.alarm(0)  # Cancel the alarm

        print(f"✅ {name} test completed successfully!")
        return True

    except Exception as e:
        print(f"❌ {name} test failed: {e}")
        return False


def create_multidataset_loaders(train_datasets_dict, val_datasets_dict, batch_size=256, num_workers=None, debug_mode=False):
    """Create DataLoader objects for multidataset training."""
    from lightning.pytorch.utilities import CombinedLoader

    # Determine number of workers
    if num_workers is None:
        num_workers = 0  # Default to 0 to avoid hanging issues

    # In debug mode, force single-threaded loading
    if debug_mode:
        num_workers = 0
        print(f"Debug mode: Using {num_workers} DataLoader workers (single-threaded)")

    # Create individual dataloaders for each dataset
    train_loaders = {}
    val_loaders = {}

    for dataset_name, train_dset in train_datasets_dict.items():
        print(f"Creating DataLoaders for {dataset_name}...")

        train_loader = DataLoader(
            train_dset,
            batch_size=batch_size,
            shuffle=True,
            num_workers=num_workers,
            pin_memory=True,
            timeout=30 if num_workers > 0 else 0
        )
        train_loaders[dataset_name] = train_loader

    for dataset_name, val_dset in val_datasets_dict.items():
        val_loader = DataLoader(
            val_dset,
            batch_size=batch_size,
            shuffle=False,
            num_workers=num_workers,
            pin_memory=True,
            timeout=30 if num_workers > 0 else 0
        )
        val_loaders[dataset_name] = val_loader

    # Test individual DataLoaders if in debug mode
    if debug_mode:
        print("\n🔍 Testing individual DataLoaders...")
        for dataset_name in train_datasets_dict.keys():
            if not debug_dataloader(train_loaders[dataset_name], f"Train {dataset_name}", max_batches=2):
                print(f"❌ Train DataLoader for {dataset_name} failed!")
                return None, None
            if not debug_dataloader(val_loaders[dataset_name], f"Val {dataset_name}", max_batches=2):
                print(f"❌ Val DataLoader for {dataset_name} failed!")
                return None, None

    # Combine loaders using Lightning's CombinedLoader
    print("Creating combined DataLoaders...")
    combined_train_loader = CombinedLoader(train_loaders, mode="max_size_cycle")
    combined_val_loader = CombinedLoader(val_loaders, mode="max_size_cycle")

    # Test combined DataLoaders if in debug mode
    if debug_mode:
        print("\n🔍 Testing combined DataLoaders...")
        if not debug_dataloader(combined_train_loader, "Combined Train", max_batches=2):
            print("❌ Combined train DataLoader failed!")
            return None, None
        if not debug_dataloader(combined_val_loader, "Combined Val", max_batches=2):
            print("❌ Combined val DataLoader failed!")
            return None, None

    return combined_train_loader, combined_val_loader


class CompileSubmodules(pl.Callback):
    """
    Compile convnet and recurrent once the model is on GPU.
    Attach to Trainer callbacks list.
    """
    def __init__(self, convnet=True, recurrent=True, compile_kwargs=None):
        self.convnet = convnet
        self.recurrent = recurrent
        self.kw = compile_kwargs or dict(mode="reduce-overhead")
        self._done = False

    def on_fit_start(self, trainer, pl_module):
        if self._done:       # only once, even if you resume training
            return

        m = pl_module.model   # your assembled core model

        if self.convnet:
            m.convnet = torch.compile(m.convnet, **self.kw)

        if self.recurrent and hasattr(m, "recurrent"):
            m.recurrent = torch.compile(m.recurrent, **self.kw)

        self._done = True
        rank = trainer.global_rank if hasattr(trainer, "global_rank") else 0
        if rank == 0:
            print("[torch.compile] convnet / recurrent compiled")


class FP16SafetyCallback(pl.Callback):
    """
    Callback to ensure critical components remain in FP32 during FP16 training.
    Forces BatchNorm, custom normalization layers, and readouts to FP32.
    """
    def __init__(self):
        self._done = False

    def on_fit_start(self, trainer, pl_module):
        if self._done or trainer.precision != "16-mixed":
            return

        model = pl_module.model
        fp32_count = 0

        # Force critical components to FP32
        for name, module in model.named_modules():
            # BatchNorm layers
            if isinstance(module, (torch.nn.BatchNorm1d, torch.nn.BatchNorm2d, torch.nn.BatchNorm3d)):
                module.float()
                fp32_count += 1

            # Custom normalization layers
            elif 'RMSNorm' in module.__class__.__name__ or 'LayerNorm' in module.__class__.__name__:
                module.float()
                fp32_count += 1

            elif 'GlobalResponseNorm' in module.__class__.__name__:
                module.float()
                fp32_count += 1

            # Readout layers
            elif 'readout' in name.lower() or 'Readout' in module.__class__.__name__:
                module.float()
                fp32_count += 1

        self._done = True
        rank = trainer.global_rank if hasattr(trainer, "global_rank") else 0
        if rank == 0:
            print(f"[FP16 Safety] Forced {fp32_count} critical components to FP32")


class GradientMonitorCallback(pl.Callback):
    """
    Callback to monitor gradients for NaN/Inf values and gradient norms during FP16 training.
    """
    def __init__(self, log_frequency=100):
        self.log_frequency = log_frequency
        self.step_count = 0

    def on_after_backward(self, trainer, pl_module):
        if trainer.precision != "16-mixed":
            return

        self.step_count += 1

        if self.step_count % self.log_frequency == 0:
            # Check for NaN/Inf gradients
            nan_count = 0
            inf_count = 0
            total_norm = 0.0
            param_count = 0

            for param in pl_module.parameters():
                if param.grad is not None:
                    grad = param.grad
                    nan_count += torch.isnan(grad).sum().item()
                    inf_count += torch.isinf(grad).sum().item()
                    total_norm += grad.norm().item() ** 2
                    param_count += 1

            total_norm = total_norm ** 0.5

            # Log gradient statistics
            pl_module.log('grad_norm', total_norm, prog_bar=False)

            if nan_count > 0 or inf_count > 0:
                print(f"[Gradient Monitor] Step {self.step_count}: NaN={nan_count}, Inf={inf_count}, Norm={total_norm:.4f}")

            # Check gradient scaler state if available
            if hasattr(trainer.strategy, 'scaler'):
                scaler = trainer.strategy.scaler
                pl_module.log('grad_scale', scaler.get_scale(), prog_bar=False)


def attach_overflow_hooks(model, threshold=5e4, clamp_activations=False, clamp_range=(-1e4, 1e4)):
    """
    Attach forward hooks to all leaf modules to detect and optionally clamp overflow/inf values.

    Args:
        model: PyTorch model to attach hooks to
        threshold: Threshold above which to report large values
        clamp_activations: If True, clamp activations to safe range
        clamp_range: (min, max) range for clamping

    Returns:
        List of hook handles (for removal if needed)
    """
    def make_hook(name):
        def hook(module, input, output):
            if isinstance(output, torch.Tensor):
                m = output.detach().abs().max().item()

                # Check for overflow/inf
                if m > threshold or torch.isinf(output).any():
                    print(f"[OVERFLOW] {name}: max|x|={m:.2e}")

                # Optionally clamp activations for safety
                if clamp_activations:
                    original_output = output
                    clamped_output = torch.clamp(output, min=clamp_range[0], max=clamp_range[1])

                    # Check if clamping actually changed anything
                    if not torch.equal(original_output, clamped_output):
                        n_clamped = ((original_output < clamp_range[0]) | (original_output > clamp_range[1])).sum().item()
                        print(f"[CLAMPED] {name}: clamped {n_clamped} values to range {clamp_range}")

                    return clamped_output

            return output
        return hook

    handles = []
    for name, module in model.named_modules():
        if len(list(module.children())) == 0:        # leaf modules only
            handle = module.register_forward_hook(make_hook(name))
            handles.append(handle)

    clamp_msg = f" with activation clamping {clamp_range}" if clamp_activations else ""
    print(f"[OVERFLOW HOOKS] Attached overflow detection hooks to {len(handles)} leaf modules{clamp_msg}")
    return handles


class OverflowDebugCallback(pl.Callback):
    """
    Callback to attach overflow detection hooks to the model for debugging inf/NaN issues.
    """
    def __init__(self, threshold=5e4, enable=True, clamp_activations=False, clamp_range=(-1e4, 1e4)):
        self.threshold = threshold
        self.enable = enable
        self.clamp_activations = clamp_activations
        self.clamp_range = clamp_range
        self.hooks = []
        self._attached = False

    def on_fit_start(self, trainer, pl_module):
        if not self.enable or self._attached:
            return

        clamp_msg = f" with activation clamping {self.clamp_range}" if self.clamp_activations else ""
        print(f"🔍 Attaching overflow detection hooks (threshold={self.threshold:.1e}){clamp_msg}...")
        self.hooks = attach_overflow_hooks(
            pl_module.model,
            threshold=self.threshold,
            clamp_activations=self.clamp_activations,
            clamp_range=self.clamp_range
        )
        self._attached = True

    def on_after_backward(self, trainer, pl_module):
        """Check for NaN gradients after backward pass."""
        if not self.enable:
            return

        nan_grads = []
        inf_grads = []

        for name, param in pl_module.named_parameters():
            if param.grad is not None:
                if torch.isnan(param.grad).any():
                    nan_count = torch.isnan(param.grad).sum().item()
                    nan_grads.append((name, nan_count))

                if torch.isinf(param.grad).any():
                    inf_count = torch.isinf(param.grad).sum().item()
                    inf_grads.append((name, inf_count))

                # Check for very large gradients
                max_grad = param.grad.abs().max().item()
                if max_grad > self.threshold:
                    print(f"[LARGE GRAD] {name}: max|grad|={max_grad:.2e}")

        if nan_grads:
            print(f"🚨 [NaN GRADIENTS] Found NaN gradients in {len(nan_grads)} parameters:")
            for name, count in nan_grads[:5]:  # Show first 5
                print(f"   {name}: {count} NaN values")

        if inf_grads:
            print(f"🚨 [INF GRADIENTS] Found inf gradients in {len(inf_grads)} parameters:")
            for name, count in inf_grads[:5]:  # Show first 5
                print(f"   {name}: {count} inf values")

    def on_fit_end(self, trainer, pl_module):
        if self.hooks:
            print("🔍 Removing overflow detection hooks...")
            for handle in self.hooks:
                handle.remove()
            self.hooks = []
            self._attached = False


class NaNDebugCallback(pl.Callback):
    """
    Callback to debug NaN losses in FP16 training.
    """
    def __init__(self, check_every_n_steps=10):
        self.check_every_n_steps = check_every_n_steps
        self.step_count = 0

    def on_train_batch_end(self, trainer, pl_module, outputs, batch, batch_idx):
        self.step_count += 1

        if self.step_count % self.check_every_n_steps == 0:
            # Check if loss is NaN
            if torch.isnan(outputs).any():
                print(f"🚨 NaN loss detected at step {self.step_count}!")
                self._debug_batch(batch, pl_module)

    def _debug_batch(self, batch_dict, pl_module):
        """Debug a batch that produced NaN loss."""
        print("🔍 Debugging NaN loss...")

        for dataset_name, batch in batch_dict.items():
            print(f"\n--- Dataset: {dataset_name} ---")

            # Check input data
            for key, value in batch.items():
                if isinstance(value, torch.Tensor):
                    nan_count = torch.isnan(value).sum().item()
                    inf_count = torch.isinf(value).sum().item()
                    print(f"  {key}: shape={value.shape}, dtype={value.dtype}, NaN={nan_count}, Inf={inf_count}")
                    if key == 'stim':
                        print(f"    stim range: [{value.min().item():.4f}, {value.max().item():.4f}]")
                    elif key == 'robs':
                        print(f"    robs range: [{value.min().item():.4f}, {value.max().item():.4f}]")

            # Check model output
            try:
                with torch.no_grad():
                    stimulus = batch['stim']
                    behavior = batch.get('behavior', None)
                    dataset_idx = 0  # Assume first dataset for debugging

                    rhat = pl_module.model(stimulus, dataset_idx, behavior)

                    nan_count = torch.isnan(rhat).sum().item()
                    inf_count = torch.isinf(rhat).sum().item()
                    neg_count = (rhat < 0).sum().item()

                    print(f"  Model output (rhat): shape={rhat.shape}, dtype={rhat.dtype}")
                    print(f"    NaN={nan_count}, Inf={inf_count}, Negative={neg_count}")
                    print(f"    Range: [{rhat.min().item():.4f}, {rhat.max().item():.4f}]")

                    # Check loss computation
                    batch_with_pred = batch.copy()
                    batch_with_pred['rhat'] = rhat

                    # Test loss computation
                    try:
                        loss = pl_module.loss_fn(batch_with_pred)
                        print(f"    Loss: {loss.item():.6f} (NaN: {torch.isnan(loss).item()})")
                    except Exception as e:
                        print(f"    Loss computation failed: {e}")

            except Exception as e:
                print(f"  Model forward failed: {e}")


class DebugCallback(pl.Callback):
    """
    Callback to help debug hanging issues during training.
    """
    def __init__(self):
        self.step_count = 0

    def on_train_batch_start(self, trainer, pl_module, batch, batch_idx):
        print(f"[Debug] Starting training batch {batch_idx}")

    def on_train_batch_end(self, trainer, pl_module, outputs, batch, batch_idx):
        print(f"[Debug] Completed training batch {batch_idx}")

    def on_validation_batch_start(self, trainer, pl_module, batch, batch_idx, dataloader_idx=0):
        print(f"[Debug] Starting validation batch {batch_idx}")

    def on_validation_batch_end(self, trainer, pl_module, outputs, batch, batch_idx, dataloader_idx=0):
        print(f"[Debug] Completed validation batch {batch_idx}")

    def on_sanity_check_start(self, trainer, pl_module):
        print("[Debug] Starting sanity check...")

    def on_sanity_check_end(self, trainer, pl_module):
        print("[Debug] Sanity check completed!")

    def on_train_start(self, trainer, pl_module):
        print("[Debug] Training started!")

    def on_validation_start(self, trainer, pl_module):
        print("[Debug] Validation started!")

    def on_validation_end(self, trainer, pl_module):
        print("[Debug] Validation ended!")



def train_multidataset_model(train_config_path, model_name=None, device=None, max_epochs=25,
                           compile=False, test_mode=False, early_stopping_patience=5, batch_size=256,
                           use_fp16=False, fp16_opt_level="O1", skip_sanity_check=False, debug_mode=False,
                           debug_dataloader=False, num_workers=None, tensor_core_precision='medium',
                           overflow_threshold=5e4, disable_overflow_hooks=False, lr=5e-4, weight_decay=1e-5,
                           optimizer='AdamW', accumulate_grad_batches=1):
    """Train a multidataset model and save checkpoints.

    Args:
        train_config_path: Path to the multidataset training configuration file
        model_name: Model name to use (if None, will be generated from config)
        device: Device specification (string or torch.device)
        max_epochs: Maximum number of epochs to train
        compile: Whether to compile the model with torch.compile
        test_mode: If True, limit batches and enable profiler for testing
        early_stopping_patience: Number of epochs with no improvement after which training will be stopped
        batch_size: Batch size for training and validation
        use_fp16: Whether to use FP16 mixed precision training
        fp16_opt_level: FP16 optimization level ("O1" for conservative, "O2" for aggressive)
        skip_sanity_check: If True, skip PyTorch Lightning sanity validation check
        debug_mode: If True, enable additional debugging features
        debug_dataloader: If True, enable DataLoader debugging
        num_workers: Number of DataLoader workers
        tensor_core_precision: Tensor Core precision ('medium', 'high', or None)
        overflow_threshold: Threshold for overflow detection hooks (default: 5e4)
        disable_overflow_hooks: If True, disable overflow detection hooks even in FP16/debug mode
        lr: Learning rate for optimizer
        weight_decay: Weight decay for optimizer
        optimizer: Optimizer type (AdamW, Adam, SGD, etc.)
        accumulate_grad_batches: Number of batches to accumulate gradients over
    """
    # Enable Tensor Core optimizations for better performance
    if tensor_core_precision and torch.cuda.is_available():
        torch.set_float32_matmul_precision(tensor_core_precision)
        print(f"🚀 Enabled Tensor Core optimization with precision='{tensor_core_precision}'")
        print("   This will significantly improve performance on modern GPUs!")

    torch.backends.cuda.matmul.allow_tf32 = True
    torch.backends.cudnn.allow_tf32  = True

    print(f"Loading multidataset training config from: {train_config_path}")

    # Load configurations using the new multidataset config loader
    model_config, dataset_configs = load_multidataset_config(train_config_path)

    print(f"Loaded model config (type: {model_config.get('model_type')})")
    print(f"Loaded {len(dataset_configs)} dataset configs")

    # Print dataset info
    for i, dataset_config in enumerate(dataset_configs):
        print(f"Dataset {i}:")
        print(f"  Frontend: {dataset_config.get('frontend', {}).get('type', 'none')}")
        print(f"  Readout: {dataset_config.get('readout', {}).get('type', 'none')}")
        print(f"  CIDs: {len(dataset_config.get('cids', []))} units")
        print(f"  Weight: {dataset_config.get('_weight', 1.0)}")

    # Prepare multidataset data
    train_datasets_dict, val_datasets_dict, updated_dataset_configs = prepare_multidataset_data(
        dataset_configs, use_fp16=use_fp16
    )

    # Create combined dataloaders
    train_loader, val_loader = create_multidataset_loaders(
        train_datasets_dict, val_datasets_dict,
        batch_size=batch_size,
        num_workers=num_workers,
        debug_mode=debug_dataloader or debug_mode
    )

    # Check if DataLoader creation failed
    if train_loader is None or val_loader is None:
        print("❌ DataLoader creation failed! Exiting...")
        return None

    # Generate model name if not provided
    if model_name is None:
        base_name = get_name_from_config(model_config)
        random_suffix = codename.codename()
        model_name = f"{base_name}_multidataset_{random_suffix}"

    print(f"Model name: {model_name}")

    # Handle device specification
    if device is None or device == 'auto':
        device = get_free_device()
        print(f"Auto-selected device: {device}")
    elif isinstance(device, str):
        if device.lower() == 'cpu':
            device = torch.device('cpu')
        elif device.lower().startswith('cuda'):
            device = torch.device(device)
        else:
            raise ValueError(f"Invalid device specification: {device}")
        print(f"Using specified device: {device}")

    # Determine accelerator and devices for PyTorch Lightning
    if device.type == 'cpu':
        accelerator = 'cpu'
        devices = 'auto'
    elif device.type == 'cuda':
        accelerator = 'gpu'
        devices = [device.index] if device.index is not None else 'auto'
    else:
        accelerator = 'auto'
        devices = 'auto'

    # Create checkpoint directory
    checkpoint_dir = Path(f"/mnt/ssd/YatesMarmoV1/conv_model_fits/runs/{model_name}")
    checkpoint_dir.mkdir(parents=True, exist_ok=True)

    # Create callbacks
    checkpoint_callback = pl.pytorch.callbacks.ModelCheckpoint(
        dirpath=str(checkpoint_dir),
        filename=model_name+'-{epoch:02d}-{val_loss_total:.4f}',
        monitor='val_loss_total',
        mode='min',
        save_top_k=2,
        save_last=True
    )

    early_stopping_callback = pl.pytorch.callbacks.EarlyStopping(
        monitor='val_loss_total',
        patience=early_stopping_patience,
        verbose=True,
        mode='min'
    )

    callbacks = [checkpoint_callback, early_stopping_callback, ValidateOnTrainStart()]

    # Add FP16 safety callback if using FP16
    if use_fp16:
        print("Adding FP16 safety callback to trainer...")
        fp16_safety_cb = FP16SafetyCallback()
        callbacks.append(fp16_safety_cb)

        print("Adding gradient monitoring callback to trainer...")
        grad_monitor_cb = GradientMonitorCallback(log_frequency=100)
        callbacks.append(grad_monitor_cb)

        print("Adding NaN debugging callback to trainer...")
        nan_debug_cb = NaNDebugCallback(check_every_n_steps=10)
        callbacks.append(nan_debug_cb)

        if not disable_overflow_hooks:
            print(f"Adding overflow detection callback to trainer (threshold={overflow_threshold:.1e})...")
            overflow_debug_cb = OverflowDebugCallback(threshold=overflow_threshold, enable=True)
            callbacks.append(overflow_debug_cb)

    # Add debug callback if requested
    if debug_mode:
        print("Adding debug callback to trainer...")
        debug_cb = DebugCallback()
        callbacks.append(debug_cb)

        # Also add overflow detection in debug mode (even without FP16)
        if not use_fp16 and not disable_overflow_hooks:
            print(f"Adding overflow detection callback to trainer (debug mode, threshold={overflow_threshold:.1e})...")
            overflow_debug_cb = OverflowDebugCallback(threshold=overflow_threshold, enable=True)
            callbacks.append(overflow_debug_cb)

    # Add compile callback if requested
    if (not 'ipykernel' in sys.modules) and compile:
        print("Adding compile callback to trainer...")
        compile_cb = CompileSubmodules(
            convnet=True,
            recurrent=True,
            compile_kwargs=dict(fullgraph=False, dynamic=True, mode="default")
        )
        callbacks.append(compile_cb)

    # Configure profiler
    profiler = None
    if test_mode:
        print("Test mode enabled: Using PyTorch profiler")
        profiler = PyTorchProfiler(
            dirpath=str(checkpoint_dir),
            filename="profiler-traces",
            schedule=torch.profiler.schedule(wait=1, warmup=1, active=3),
            record_shapes=True,
            profile_memory=True,
            with_stack=True
        )
    else:
        profiler = "simple"

    # Calculate baseline firing rates for each dataset
    print("\nCalculating baseline firing rates for each dataset...")
    baseline_rates_list = []

    for dataset_idx, (dataset_name, dataset_config) in enumerate(zip(train_datasets_dict.keys(), updated_dataset_configs)):
        train_dset = train_datasets_dict[dataset_name]

        fr = 0
        n = 0
        for dset in train_dset.dsets:
            robs = dset.covariates['robs']
            # Use float32 for sum to avoid FP16 overflow, then cast back
            fr += robs.float().sum(0)
            n += robs.shape[0]

        
        baseline_rates = fr / n
        baseline_rates_list.append(baseline_rates)

        print(f"Dataset {dataset_idx}: {len(dataset_config['cids'])} units, "
              f"mean rate: {baseline_rates.mean().item():.4f}")

    # Store raw baseline rates - we'll transform them after creating the model
    # based on the model's activation type
    raw_baseline_rates_list = baseline_rates_list

    # Adjust learning rate for FP16 training if needed
    final_lr = lr
    if use_fp16 and lr == 5e-4:  # Only adjust if using default learning rate
        final_lr = 1.5e-4  # Standard reduction for FP16
        print(f"Reduced learning rate to {final_lr} for FP16 training")
    else:
        print(f"Using learning rate: {final_lr}")

    # Create Lightning module with proper model instantiation for v1multi
    def create_multidataset_model(model_config, dataset_configs):
        """Create a multidataset model with proper configuration."""
        return build_model(model_config, dataset_configs)

    # Debug: Print the optimizer kwargs to see what types we're passing
    optim_kwargs = {'lr': final_lr, 'weight_decay': weight_decay, 'eps': 1e-4 if use_fp16 else 1e-8}
    print(f"Debug: Optimizer kwargs being passed to Lightning module:")
    for key, value in optim_kwargs.items():
        print(f"  {key}: {value} (type: {type(value).__name__})")

    pl_model = MultiDatasetPLCore(
        model_class=create_multidataset_model,
        model_config=model_config,
        dataset_configs=updated_dataset_configs,
        optimizer=optimizer,
        optim_kwargs=optim_kwargs,
        accumulate_grad_batches=accumulate_grad_batches,
        dataset_info=updated_dataset_configs
    )

    # Configure trainer
    trainer_kwargs = {
        "callbacks": callbacks,
        "accelerator": accelerator,
        "devices": devices,
        "logger": WandbLogger(
            project='Digital Twin Multidataset',
            name=model_name,
            save_code=True,
            entity='yateslab',
            save_dir=str(checkpoint_dir),
        ),
        "default_root_dir": str(checkpoint_dir),
        "max_epochs": max_epochs if not test_mode else 1,
        "check_val_every_n_epoch": 1,
        "profiler": profiler,
    }

    # Add FP16 configuration if requested
    if use_fp16:
        # Use more conservative gradient scaling for stability
        init_scale = 256  # Much lower than default 2**16
        growth_interval = 2000  # More frequent scaling adjustments

        try:
            # Try new API first (PyTorch 2.0+)
            from torch.amp import GradScaler
            scaler = GradScaler('cuda', init_scale=init_scale, growth_interval=growth_interval)
        except ImportError:
            # Fall back to old API
            from torch.cuda.amp import GradScaler
            scaler = GradScaler(init_scale=init_scale, growth_interval=growth_interval)

        print(f"Using conservative gradient scaling: init_scale={init_scale}, growth_interval={growth_interval}")

        from lightning.pytorch.plugins.precision import MixedPrecision   # works ≥2.2

        precision_plugin = MixedPrecision(
            precision="16-mixed",
            device="cuda",
            scaler=scaler,
        )
        
        trainer_kwargs.update(
            # precision="16-mixed",
            plugins=[precision_plugin],
            # amp_scaler_kwargs=dict(init_scale=args.fp16_init_scale,
            #                    growth_interval=2000)
        )

        # Use aggressive gradient clipping for FP16 stability
        trainer_kwargs["gradient_clip_val"] = 0.25  # Very aggressive clipping
        trainer_kwargs["gradient_clip_algorithm"] = "norm"  # Clip by norm, not value
        print(f"FP16 training enabled with aggressive gradient clipping=0.25 (norm)")

    # Skip sanity check if requested (useful for debugging hanging issues)
    if skip_sanity_check:
        trainer_kwargs["num_sanity_val_steps"] = 0
        print("Skipping PyTorch Lightning sanity validation check")

    # Add debug options if requested
    if debug_mode:
        trainer_kwargs["detect_anomaly"] = True
        # Note: track_grad_norm is deprecated in newer PyTorch Lightning versions
        # trainer_kwargs["track_grad_norm"] = 2
        print("Debug mode enabled: anomaly detection")

    # Add batch limits if in test mode
    if test_mode:
        trainer_kwargs.update({
            "limit_train_batches": 5,
            "limit_val_batches": 3,
        })

    # Transform baseline rates based on model's activation type
    print("\nDetermining bias initialization based on model activation...")

    # Check the model's activation type
    model_activation = pl_model.model.activation
    activation_name = model_activation.__class__.__name__
    print(f"Model activation: {activation_name}")

    if activation_name == 'Identity':
        # For Identity activation, model outputs log rates
        # So bias should be log(baseline_rates)
        print("Using log(baseline_rates) for bias initialization (Identity activation)")
        init_bias_list = []
        for i, rates in enumerate(raw_baseline_rates_list):
            log_rates = torch.log(rates.float() + 1e-8)  # Add small epsilon to avoid log(0)
            init_bias_list.append(log_rates.to(robs.dtype))
            print(f"Dataset {i}: log rates range [{log_rates.min().item():.4f}, {log_rates.max().item():.4f}]")

    else:
        # For softplus or other activations, use inverse transformation
        print(f"Using inverse activation transformation for bias initialization ({activation_name})")

        if hasattr(model_activation, '__name__') and 'softplus' in model_activation.__name__.lower():
            # Use stable inverse softplus
            def stable_inv_softplus(x, beta=1):
                x = torch.clamp(x, min=1e-7, max=100.0)
                return torch.log(x + 1e-8)  # Stable approximation for small values

            init_bias_list = []
            for i, rates in enumerate(raw_baseline_rates_list):
                inv_rates = stable_inv_softplus(rates)
                init_bias_list.append(inv_rates)
                print(f"Dataset {i}: inv_softplus rates range [{inv_rates.min().item():.4f}, {inv_rates.max().item():.4f}]")
        else:
            # For other activations, just use the raw rates
            print("Using raw baseline rates (unknown activation)")
            init_bias_list = raw_baseline_rates_list

    # Initialize model components
    initialize_model_components(pl_model.model, init_bias=init_bias_list)

    # Create trainer and train
    trainer = pl.Trainer(**trainer_kwargs)

    # Start timing
    start_time = time.time()

    # Train the model
    trainer.fit(pl_model, train_loader, val_loader)

    # End timing
    end_time = time.time()
    training_time = end_time - start_time

    # Get the best model path
    best_model_path = checkpoint_callback.best_model_path

    # Print training summary
    print("\n===== MULTIDATASET TRAINING SUMMARY =====")
    print(f"Total training time: {training_time:.2f} seconds")
    print(f"Number of datasets: {len(dataset_configs)}")

    for i, dataset_config in enumerate(updated_dataset_configs):
        print(f"Dataset {i}: {len(dataset_config['cids'])} units")

    # Print metrics
    print("\nTraining metrics:")
    for metric_name, metric_value in trainer.callback_metrics.items():
        if isinstance(metric_value, torch.Tensor):
            print(f"- {metric_name}: {metric_value.item():.6f}")
        else:
            print(f"- {metric_name}: {metric_value}")

    print("==========================================")

    # Register the model
    model_id = f"{model_name}_epoch_{trainer.current_epoch}"

    # Create metadata for multidataset model
    metadata = {
        'epochs': trainer.current_epoch,
        'training_time': training_time,
        'batch_size': batch_size,
        'compiled': compile,
        'num_datasets': len(dataset_configs),
        'total_units': sum(len(config['cids']) for config in updated_dataset_configs)
    }

    registry.register_model(
        model_id=model_id,
        checkpoint_path=best_model_path,
        config_path=str(train_config_path),  # Store the training config path
        metrics={'val_loss_total': trainer.callback_metrics.get('val_loss_total', 0).item()},
        metadata=metadata,
        dataset_config_path=[config.get('_config_path', '') for config in updated_dataset_configs]
    )

    return best_model_path

#%% Main execution
def parse_arguments():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(description='Train multidataset neural network models with FP16 support')

    # Required training configuration
    parser.add_argument('--train-config', type=str, required=True,
                        help='Path to multidataset training configuration file')

    # Training parameters - all default to None to allow config fallback
    parser.add_argument('--compile', action='store_true', default=None,
                        help='Enable model compilation with torch.compile')

    parser.add_argument('--test', action='store_true', default=None,
                        help='Run in test mode with limited batches and profiling')

    parser.add_argument('--max-epochs', type=int, default=None,
                        help='Maximum number of training epochs')

    parser.add_argument('--batch-size', type=int, default=None,
                        help='Batch size for training and validation')

    parser.add_argument('--early-stopping-patience', type=int, default=None,
                        help='Number of epochs with no improvement after which training will be stopped')

    parser.add_argument('--device', type=str, default=None,
                        help='Device to use for training. Options: "auto", "cpu", "cuda", "cuda:0", etc. Default: "auto" (automatically select best available device)')

    parser.add_argument('--no-codename', action='store_true', default=None,
                        help='Disable appending a random codename to the model name (codename is appended by default for uniqueness)')

    # FP16 mixed precision training
    parser.add_argument('--fp16', action='store_true', default=None,
                        help='Enable FP16 mixed precision training for memory efficiency and speed')

    parser.add_argument('--fp16-opt-level', type=str, default=None, choices=['O1', 'O2'],
                        help='FP16 optimization level: O1 (conservative) or O2 (aggressive). Default: O1')

    # Debugging options
    parser.add_argument('--skip-sanity-check', action='store_true', default=None,
                        help='Skip PyTorch Lightning sanity validation check (useful if hanging)')

    parser.add_argument('--debug', action='store_true', default=None,
                        help='Enable debug mode with anomaly detection and gradient tracking')

    parser.add_argument('--debug-dataloader', action='store_true', default=None,
                        help='Enable DataLoader debugging (test data loading before training)')

    parser.add_argument('--num-workers', type=int, default=None,
                        help='Override number of DataLoader workers (default: auto-detect)')

    parser.add_argument('--overflow-threshold', type=float, default=None,
                        help='Threshold for overflow detection hooks (default: 5e4)')

    parser.add_argument('--disable-overflow-hooks', action='store_true', default=None,
                        help='Disable overflow detection hooks even in FP16/debug mode')

    # Advanced FP16 options
    parser.add_argument('--fp16-conservative', action='store_true', default=None,
                        help='Use more conservative FP16 settings (lower LR, more clipping)')

    parser.add_argument('--fp16-init-scale', type=float, default=None,
                        help='Initial gradient scaling for FP16 (default: 65536)')

    # Tensor Core optimization
    parser.add_argument('--tensor-core-precision', type=str, default=None,
                        choices=['medium', 'high', 'highest', 'none'],
                        help='Tensor Core precision for matmul operations (default: medium)')

    # Optimizer parameters
    parser.add_argument('--lr', type=float, default=None,
                        help='Learning rate for optimizer')

    parser.add_argument('--weight-decay', type=float, default=None,
                        help='Weight decay for optimizer')

    parser.add_argument('--optimizer', type=str, default=None,
                        help='Optimizer type (AdamW, Adam, SGD, etc.)')

    # Gradient accumulation
    parser.add_argument('--accumulate-grad-batches', type=int, default=None,
                        help='Number of batches to accumulate gradients over')

    return parser.parse_args()


def merge_config_with_args(train_config_path, args):
    """
    Merge command-line arguments with config file values.
    Command-line arguments take precedence when provided (not None).

    Args:
        train_config_path: Path to the training config file
        args: Parsed command-line arguments

    Returns:
        Dictionary with merged configuration values
    """
    def safe_float(value, default):
        """Safely convert value to float, handling string scientific notation."""
        if value is None:
            return default
        try:
            return float(value)
        except (ValueError, TypeError):
            return default

    def safe_int(value, default):
        """Safely convert value to int."""
        if value is None:
            return default
        try:
            return int(value)
        except (ValueError, TypeError):
            return default

    def safe_bool(value, default):
        """Safely convert value to bool."""
        if value is None:
            return default
        if isinstance(value, bool):
            return value
        if isinstance(value, str):
            return value.lower() in ('true', 'yes', '1', 'on')
        return bool(value)

    # Load the training config to get default values
    train_config_path = Path(train_config_path)
    with open(train_config_path, 'r') as f:
        train_config = yaml.safe_load(f)

    # Extract relevant sections from config
    training_config = train_config.get('training', {})
    optimizer_config = train_config.get('optimizer', {})

    # Create merged config with fallbacks and proper type conversion
    merged = {}

    # Training parameters (integers)
    merged['max_epochs'] = safe_int(args.max_epochs if args.max_epochs is not None else training_config.get('max_epochs'), 25)
    merged['batch_size'] = safe_int(args.batch_size if args.batch_size is not None else training_config.get('batch_size'), 256)
    merged['early_stopping_patience'] = safe_int(args.early_stopping_patience if args.early_stopping_patience is not None else training_config.get('early_stopping_patience'), 5)
    merged['accumulate_grad_batches'] = safe_int(args.accumulate_grad_batches if args.accumulate_grad_batches is not None else training_config.get('accumulate_grad_batches'), 1)

    # FP16 settings
    merged['use_fp16'] = safe_bool(args.fp16 if args.fp16 is not None else training_config.get('fp16'), False)
    merged['fp16_opt_level'] = args.fp16_opt_level if args.fp16_opt_level is not None else training_config.get('fp16_opt_level', 'O1')
    merged['fp16_conservative'] = safe_bool(args.fp16_conservative if args.fp16_conservative is not None else training_config.get('fp16_conservative'), False)
    merged['fp16_init_scale'] = safe_float(args.fp16_init_scale if args.fp16_init_scale is not None else training_config.get('fp16_init_scale'), 2**16)

    # Optimizer settings (floats - this is the critical fix!)
    merged['lr'] = safe_float(args.lr if args.lr is not None else optimizer_config.get('lr'), 5e-4)
    merged['weight_decay'] = safe_float(args.weight_decay if args.weight_decay is not None else optimizer_config.get('weight_decay'), 1e-5)
    merged['optimizer'] = args.optimizer if args.optimizer is not None else optimizer_config.get('type', 'AdamW')

    # Device and hardware settings
    merged['device'] = args.device if args.device is not None else training_config.get('device', 'auto')
    merged['tensor_core_precision'] = args.tensor_core_precision if args.tensor_core_precision is not None else training_config.get('tensor_core_precision', 'medium')
    merged['num_workers'] = safe_int(args.num_workers if args.num_workers is not None else training_config.get('num_workers'), None) if args.num_workers is not None or training_config.get('num_workers') is not None else None

    # Compilation and optimization (booleans)
    merged['compile'] = safe_bool(args.compile if args.compile is not None else training_config.get('compile'), False)

    # Debugging options (booleans and floats)
    merged['debug'] = safe_bool(args.debug if args.debug is not None else training_config.get('debug'), False)
    merged['debug_dataloader'] = safe_bool(args.debug_dataloader if args.debug_dataloader is not None else training_config.get('debug_dataloader'), False)
    merged['skip_sanity_check'] = safe_bool(args.skip_sanity_check if args.skip_sanity_check is not None else training_config.get('skip_sanity_check'), False)
    merged['overflow_threshold'] = safe_float(args.overflow_threshold if args.overflow_threshold is not None else training_config.get('overflow_threshold'), 5e4)
    merged['disable_overflow_hooks'] = safe_bool(args.disable_overflow_hooks if args.disable_overflow_hooks is not None else training_config.get('disable_overflow_hooks'), False)

    # Test mode and other flags (booleans)
    merged['test_mode'] = safe_bool(args.test if args.test is not None else training_config.get('test'), False)
    merged['no_codename'] = safe_bool(args.no_codename if args.no_codename is not None else training_config.get('no_codename'), False)

    return merged


if __name__ == "__main__":
    # Parse command-line arguments
    args = parse_arguments()

    # Merge command-line arguments with config file values
    config = merge_config_with_args(args.train_config, args)

    print("=== MULTIDATASET TRAINING WITH FP16 SUPPORT ===")
    print(f"Training config: {args.train_config}")
    print(f"Device: {config['device']}")
    print(f"Compilation enabled: {config['compile']}")
    print(f"Test mode: {config['test_mode']}")
    print(f"Max epochs: {config['max_epochs']}")
    print(f"Batch size: {config['batch_size']}")
    print(f"Early stopping patience: {config['early_stopping_patience']}")
    print(f"Learning rate: {config['lr']}")
    print(f"Optimizer: {config['optimizer']}")
    print(f"Append codename: {not config['no_codename']}")
    print(f"FP16 training: {config['use_fp16']}")
    if config['use_fp16']:
        print(f"FP16 optimization level: {config['fp16_opt_level']}")

    # Generate model name with codename if requested
    model_name = None
    if not config['no_codename']:
        # We'll generate the name in the training function
        pass

    # Train multidataset model
    best_model_path = train_multidataset_model(
        train_config_path=args.train_config,
        model_name=model_name,
        device=config['device'],
        max_epochs=config['max_epochs'],
        compile=config['compile'],
        test_mode=config['test_mode'],
        early_stopping_patience=config['early_stopping_patience'],
        batch_size=config['batch_size'],
        use_fp16=config['use_fp16'],
        fp16_opt_level=config['fp16_opt_level'],
        skip_sanity_check=config['skip_sanity_check'],
        debug_mode=config['debug'],
        debug_dataloader=config['debug_dataloader'],
        num_workers=config['num_workers'],
        tensor_core_precision=config['tensor_core_precision'] if config['tensor_core_precision'] != 'none' else None,
        overflow_threshold=config['overflow_threshold'],
        disable_overflow_hooks=config['disable_overflow_hooks'],
        lr=config['lr'],
        weight_decay=config['weight_decay'],
        optimizer=config['optimizer'],
        accumulate_grad_batches=config['accumulate_grad_batches']
    )

    print(f"\n🎉 Multidataset training complete!")
    print(f"Best model saved to: {best_model_path}")
    print("\nDone!")